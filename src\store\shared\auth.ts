import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { type DataInfo, setToken, removeToken, userKey, getToken } from "@/utils/auth";
import { storageLocal } from "@/utils/responsive";
import { router } from "@/router/utils";

export interface AuthUser {
  avatar: string;
  username: string;
  nickname: string;
  roles: Array<string>;
  permissions: Array<string>;
}

export interface AuthState {
  user: AuthUser | null;
  isRemembered: boolean;
  loginDay: number;
}

/**
 * 共享认证 Store
 * 供管理系统和收银系统共同使用
 */
export const useAuthStore = defineStore("shared-auth", () => {
  // 状态
  const user = ref<AuthUser | null>(null);
  const isRemembered = ref(false);
  const loginDay = ref(7);

  // 计算属性
  const isLoggedIn = computed(() => !!getToken() && !!user.value);
  const userRoles = computed(() => user.value?.roles || []);
  const userPermissions = computed(() => user.value?.permissions || []);

  // 初始化用户信息
  const initUserInfo = () => {
    const userData = storageLocal().getItem<DataInfo<number>>(userKey);
    if (userData) {
      user.value = {
        avatar: userData.avatar || "",
        username: userData.username || "",
        nickname: userData.nickname || "",
        roles: userData.roles || [],
        permissions: userData.permissions || []
      };
    }
  };

  // 设置用户信息
  const setUserInfo = (userInfo: Partial<AuthUser>) => {
    if (!user.value) {
      user.value = {
        avatar: "",
        username: "",
        nickname: "",
        roles: [],
        permissions: []
      };
    }
    
    Object.assign(user.value, userInfo);
    
    // 同步到本地存储
    const currentData = storageLocal().getItem<DataInfo<number>>(userKey) || {};
    storageLocal().setItem(userKey, { ...currentData, ...userInfo });
  };

  // 登录
  const login = async (credentials: any) => {
    try {
      // 这里应该调用实际的登录 API
      // const response = await loginApi(credentials);
      
      // 模拟登录成功
      const mockUser: AuthUser = {
        avatar: "",
        username: credentials.username || "收银员",
        nickname: credentials.username || "收银员",
        roles: ["cashier"],
        permissions: ["cashier:read", "cashier:write"]
      };
      
      // 设置 token (这里应该从 API 响应中获取)
      setToken("mock-token-" + Date.now());
      
      // 设置用户信息
      setUserInfo(mockUser);
      
      return { success: true, data: mockUser };
    } catch (error) {
      console.error("登录失败:", error);
      return { success: false, error };
    }
  };

  // 登出
  const logout = () => {
    user.value = null;
    removeToken();
    storageLocal().removeItem(userKey);
    
    // 跳转到登录页
    router.push("/login");
  };

  // 检查权限
  const hasRole = (role: string): boolean => {
    return userRoles.value.includes(role);
  };

  const hasPermission = (permission: string): boolean => {
    return userPermissions.value.includes(permission);
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  // 初始化
  initUserInfo();

  return {
    // 状态
    user,
    isRemembered,
    loginDay,
    
    // 计算属性
    isLoggedIn,
    userRoles,
    userPermissions,
    
    // 方法
    initUserInfo,
    setUserInfo,
    login,
    logout,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission
  };
});

// 便捷的 hook 函数
export const useAuth = () => useAuthStore();
