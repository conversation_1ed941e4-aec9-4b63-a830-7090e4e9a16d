import test2 from "@/views/rms/workbench/test.vue";  
import { addDialog } from "@/components/ReDialog";
import { message, closeAllMessage } from "@/utils/message";

import {  ref,h} from "vue";
export function useUser() {
  const bookListData = ref({});
  SalBookList();
  //弹窗逻辑
  function openDialog(title = "新增") { 
    addDialog({
      title: `${title}用户`,
      draggable: true,
      contentRenderer: () => h(test2),
      beforeSure: (done)=>{ 
        message(`消息提醒`, {
            type: "success"
        });
         done(); // 关闭弹框
      }
    })

  }

  //获取预约数据
  function SalBookList() { 
    bookListData.value=[
      {
        date: '1001',
        name: '王小虎',
        status: '1', // 1-正常 2-警告 3-危险
        number:1
      },
      {
        date: '1002',
        name: '张三',
        status: '2',
      },
      {
        date: '1003',
        name: '李四',
        status: '3',
      },
      {
        date: '1004',
        name: '王五',
        status: '1',
      }
    ];
  }

  return {openDialog,bookListData}
}


