<script setup>
import './utils/index.scss'
// 动态生成楼层数和房间数
function generateBuildingData() {
  const totalFloors = 5;          // 总楼层数
  const totalRooms = 150;         // 总房间数
  const roomsPerFloorMin = 20;    // 每层最少房间数
  const roomsPerFloorMax = 40;    // 每层最多房间数
  
  // 创建楼层数组
  const floors = [];
  let remainingRooms = totalRooms;
  
  // 为前4层随机分配房间数
  for (let i = 0; i < totalFloors - 1; i++) {
    // 确保剩余房间数足够分配给后续楼层
    const maxRoomsThisFloor = Math.min(
      roomsPerFloorMax, 
      remainingRooms - (totalFloors - i - 1) * roomsPerFloorMin
    );
    
    const roomsThisFloor = Math.floor(
      Math.random() * (maxRoomsThisFloor - roomsPerFloorMin + 1)
    ) + roomsPerFloorMin;
    
    floors.push({
      number: i + 1,  // 从2层开始
      rooms: generateRooms(roomsThisFloor, i + 1)
    });
    remainingRooms -= roomsThisFloor;
  }
  
  // 最后一层分配剩余所有房间
  floors.push({
    number: totalFloors,  // 最后一层的编号
    rooms: generateRooms(remainingRooms, totalFloors)
  });
  return floors;
}
// 获取状态文本
const getStatusText = (status)=> {
      switch(status) {
        case 'available': return '可用';
        case 'occupied': return '已占用';
        default: return status;
      }
    }
// 生成指定数量的房间数据
function generateRooms(count, floorNumber) {
  const rooms = [];
  
  for (let i = 1; i <= count; i++) {
    // 房间号格式：楼层号+房间序号
    const roomNumber = `${floorNumber}0${i}`;
    
    // 随机状态：70%概率为available，30%概率为occupied
    const status = Math.random() > 0.7 ? 'occupied' : 'available';
    const roomName = Math.random() > 0.2 ? 'VIP房' : '普通房';
    const birthday = Math.random() > 0.05 ? 'false' : 'true';
    const wechat = Math.random() > 0.1 ? 'false' : 'true';
    
    rooms.push({
      number: roomNumber,
      status: status,
      birthday: birthday,
      wechat: wechat,
      roomName:roomName
    });
  }
  
  return rooms;
}

// 生成数据并打印
const floors = generateBuildingData();
</script>

<template>
 <div class="floor-container">
    <!-- 外层循环：遍历每个楼层 -->
    <div v-for="(floor, floorIndex) in floors" :key="floorIndex" class="floor-wrapper">
      <span class="floor-title">
        <span class="floor-number">区域{{ floor.number }}&nbsp; &nbsp;&nbsp;(20/30)</span>
      </span>
      <!-- 房间网格布局 -->
       <!-- 弹性布局容器，自动换行 -->
      <div class="room-flexbox">
        <div 
          v-for="(room, roomIndex) in floor.rooms" 
          :key="roomIndex" 
          class="room-item"
          :class="{ 'bg-available': room.status === 'available', 'bg-occupied': room.status === 'occupied' }"
        >
          <div class="room-number">{{ room.number }}</div>
          <div class="room-status">{{ room.roomName }}</div>
          <div class="icon-container">
          <div class="room-icon" >
            <IconifyIconOnline
             v-if="room.birthday === 'true'"
      icon="icon-park-solid:birthday-cake"
      width="13px"
      height="13px"
    style="display:inline-block;margin: 0 auto;"
    />
          </div>
          <div class="room-icon"  v-if="room.wechat === 'true'">
            <IconifyIconOnline
      icon="ic:baseline-wechat"
      width="13px"
      height="13px"
    style="display:inline-block;margin: 0 auto;"
    />
          </div>
          </div>

        </div>
      </div>
    </div>
    </div>
</template>
<style>
</style>

