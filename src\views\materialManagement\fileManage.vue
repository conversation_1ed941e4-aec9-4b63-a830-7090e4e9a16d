<template>
  <div class="class-management">
    <div class="common-layout" style="height: 100%">
      <el-container style="height: 100%">
        <el-aside width="260px">
          <el-scrollbar>
            <el-radio-group style="margin-bottom: 20px">
              <el-radio-button @click="btnShowAddFolder" :value="false">
                <el-icon><FolderAdd /></el-icon>
                添加文件
              </el-radio-button>
            </el-radio-group>
            <el-menu
              :default-openeds="['1', '3']"
              class="el-menu-vertical-demo"
              @select="handleFolderMenuSelect"
            >
              <el-sub-menu index="1">
                <template #title>
                  <el-icon><FolderOpened /></el-icon>
                  <span>素材分类</span>
                </template>
                <el-menu-item
                  v-for="(fd, index) in folderList"
                  :keys="fd.FolderID"
                  :index="fd.FolderID.toString()"
                >
                  <el-icon><Folder /></el-icon>
                  <span>{{ fd.FolderName }}</span>
                </el-menu-item>
              </el-sub-menu>
            </el-menu>
          </el-scrollbar>
        </el-aside>
        <el-container>
          <el-header>
            <!-- 操作栏 -->
            <div class="operation-bar">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索素材..."
                clearable
                class="search-input"
                @clear="applyFilters"
                @change="applyFilters"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="applyFilters">
                <el-icon><Search /></el-icon>
                查询素材
              </el-button>
              <el-button type="success" @click="showDialog('add')">
                <el-icon><Plus /></el-icon>
                上传素材
              </el-button>
              <el-button type="primary" @click="movebtn">
                <el-icon><CopyDocument /></el-icon>
                移动素材
              </el-button>
            </div>
          </el-header>
          <el-main style="height: 60%">
            <!-- 素材卡片列表 -->
            <el-row
              v-if="pagedData.length"
              :gutter="30"
              v-loading="loading"
              class="class-list"
            >
              <el-col
                v-for="item in pagedData"
                :key="item.FileID"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
              >
                <el-card class="media-card">
                  <div v-if="checkboxISShow" class="checkbox-container">
                    <el-checkbox
                      v-model="item.Movechecked"
                      @change="handlecheckMoveChange(item.Movechecked)"
                      label="移动"
                      size="large"
                    />
                  </div>
                  <!-- 图片类型 -->
                  <img
                    v-if="isImage(item.FilePath)"
                    :src="fullUrl(item.FilePath)"
                    class="media-content"
                  />

                  <!-- 视频类型 -->
                  <video
                    v-else-if="isVideo(item.FilePath)"
                    :src="fullUrl(item.FilePath)"
                    class="media-content"
                    controls
                  ></video>

                  <!-- 音频类型 -->
                  <audio
                    v-else-if="isAudio(item.FilePath)"
                    :src="fullUrl(item.FilePath)"
                    class="audio-player"
                    controls
                  ></audio>

                  <div class="card-footer">
                    <span class="title">{{ item.FileName }}</span>
                    <el-popconfirm
                      title="确认删除该素材?"
                      @confirm="deleteFile(item.FileID)"
                    >
                      <template #reference>
                        <el-button type="danger" plain>删除</el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-empty v-else description="未查询到记录" />

            <div v-if="checkboxISShow" class="fixed-buttons">
              <el-button
                :disabled="isMoveDisabled"
                @click="CheckMoveBtn"
                type="primary"
              >
                <el-icon><Check /></el-icon>
                确认移动
              </el-button>
              <el-button @click="CloseMoveBtn" type="danger">
                <el-icon><Close /></el-icon>
                取消移动</el-button
              >
            </div>
          </el-main>
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.Rows"
            :total="pagination.Records"
            :page-sizes="[5, 10, 15]"
            layout="total, sizes, prev, pager, next, jumper"
            class="pagination"
            @size-change="applyFilters"
            @current-change="applyFilters"
          />
        </el-container>
      </el-container>
    </div>
    <!-- 上传素材框 -->
    <el-dialog v-model="dialogVisible" title="素材上传" width="500px">
      <el-upload
        multiple
        drag
        :auto-upload="false"
        :on-change="handleChange"
        :on-remove="handleRemove"
        :file-list="fileList"
        accept=".jpg,.png,.mp4,.mov,.mp3,.wav"
      >
        <el-icon :size="50" color="#409EFC"><UploadFilled /></el-icon>
        <div class="upload-text">
          点击或拖拽文件到此处上传<br />
          支持格式：图片(JPG/PNG)、视频(MP4/MOV)、音频(MP3/WAV)
        </div>
      </el-upload>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="FileUpload">开始上传</el-button>
      </template>
    </el-dialog>
    <!-- 添加文件夹对话框 -->
    <el-dialog v-model="dialogFolderVisible" title="添加文件夹" width="500px">
      <el-form
        ref="formRef"
        :model="folderData"
        :rules="FolderAddformRules"
        label-width="80px"
      >
        <el-form-item label="文件夹名称" label-width="100px" prop="FolderName">
          <el-input v-model="folderData.FolderName" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogFolderVisible = false">取消</el-button>
        <el-button type="primary" @click="submitFolder">确认</el-button>
      </template>
    </el-dialog>
    <!-- 选择文件夹的信息框 -->
    <el-dialog v-model="FolderdialogVisible" title="文件夹信息" width="500px">
      <div class="mt-4">
        <el-button type="primary" @click="revertValueBtn">开始移动</el-button>
        <el-input
          v-model="searchFolderNameKeyword"
          style="width: 200px"
          placeholder="请输入文件夹名称"
          class="input-with-select ml-2"
        >
          <template #append>
            <el-button @click="QuerySelectFolderBtn" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="文件夹列表" name="FolderSelectlist">
          <el-table
            :data="FolderShowData"
            row-key="FolderID"
            style="width: 100%; height: 500px"
            @row-click="handleRowClick"
          >
            <!--<el-table-column type="selection" width="60" />-->
            <el-table-column width="55">
              <template #default="scope">
                <el-radio
                  v-model="selectedRowId"
                  :label="scope.row.id"
                  @change="handleRadioChange(scope.row)"
                ></el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="FolderID" label="文件夹编码" width="180" />
            <el-table-column prop="FolderName" label="文件夹名称" width="180" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- 投放任务列表分页 -->
      <el-pagination
        v-model:current-page="Folderpagination.current"
        v-model:page-size="Folderpagination.Rows"
        size="small"
        background
        layout="prev, pager, next"
        :total="Folderpagination.Records"
        @size-change="handleFolderPaginationChange"
        @current-change="handleFolderPaginationChange"
        class="mt-4 ml-14"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import {
  Search,
  Plus,
  CopyDocument,
  Edit,
  Delete,
  Key,
  Clock,
  Cpu,
  UploadFilled,
  FolderOpened,
  Folder,
  FolderAdd,
  Check,
  Close
} from "@element-plus/icons-vue";

import axios from "axios";
import { ElMessage } from "element-plus";
import {
  getAsyncFile,
  UploadAsyncFile,
  UpdateAsyncFile,
  DeleteAsyncFile
} from "@/api/materialManage/file";
import {
  getFolderAsync,
  AddFolderAsyncFile,
  GetByIdAsyncFolder
} from "@/api/materialManage/folder";

// // 类型定义
// var MediaItem= reactive({
//   title: "",
//   url: "",
//   type: 'image' | 'video' | 'audio',  // 可选的后端类型标识
//   cover: "",  // 视频封面
// });
// // 响应式数据
// const mediaList = ref<MediaItem>([]);
const defaultCover = ref("https://placeholder.com/video-cover.jpg");

// 素材数据相关
const loading = ref(false);
const checkboxISShow = ref(false);
const dialogFolderVisible = ref(false);
const modelList = ref([]); //原始数据
const folderList = ref([]);
const filteredData = ref([]); //过滤数据
const fileList = ref([]);
const searchKeyword = ref("");
const searchFolderNameKeyword = ref("");
const FolderShowData = ref({});
const FolderdialogVisible = ref(false);
const isMoveDisabled = ref(true);
const activeName = ref("FolderSelectlist");
const selectedRows = ref([]); // 存储选中行数据
const selectedRowId = ref(null);

//分页
var pagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});

var Folderpagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add");
const formData = reactive({
  DeviceID: 0,
  DeviceName: "",
  DeviceModel: "",
  DeviceVersion: "",
  DeviceOrientation: "",
  DeviceHeight: 0,
  DeviceWidth: 0,
  DeviceResolution: "",
  StoreId: 0,
  OnlineStatusCode: 1
});

// 表单验证规则
const FolderAddformRules = {
  FolderName: [
    { required: true, message: "请输入文件夹名称", trigger: "blur" },
    { min: 1, max: 20, message: "长度1-20个字符", trigger: "blur" }
  ]
  // DeviceModel: [
  //   { required: true, message: "请输入设备型号", trigger: "blur" },
  //   { min: 2, max: 20, message: "长度2-20个字符", trigger: "blur" }
  //   //{ pattern: /^[A-Z0-9]{6,10}$/, message: "格式不正确", trigger: "blur" }
  // ]
};

const folderData = reactive({
  FolderID: 0,
  FolderName: ""
});
// 路径处理方法
const fullUrl = url => {
  if (url.startsWith("http")) return url;
  return `https://hdcore.tang-hui.com.cn/${url}`;
};

// 类型判断方法（根据文件后缀）
const isImage = url => /\.(png|jpg|jpeg|gif|webp)$/i.test(url);
const isVideo = url => /\.(mp4|mov|avi|webm)$/i.test(url);
const isAudio = url => /\.(mp3|wav|ogg)$/i.test(url);

// 文件移除处理
const handleRemove = file => {
  const index = fileList.value.indexOf(file);
  fileList.value.splice(index, 1);
};

// 复选框变化时更新选中数据
const handleSelectionChange = val => {
  selectedRows.value = val;
};

const handleRowClick = row => {
  selectedRowId.value = row.id;
};

// 点击按钮打开新增文件夹界面
const btnShowAddFolder = file => {
  folderData.FolderID = 0;
  folderData.FolderName = "";
  dialogFolderVisible.value = true;
};

const handleMoveChange = id => {
  //this.$emit("change", { [id]: this.checked[id] });
};

//点击返回按钮返回设备数据
const revertValueBtn = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.warning("未选择移动文件夹！");
    return;
  }
  // 深拷贝选中的数据
  const copiedData = JSON.parse(JSON.stringify(selectedRows.value));
  //获取到选中素材
  const cheModelList = modelList.value.filter(item => item.Movechecked);

  // 合并到表格中（避免重复）
  const existingIds = folderList.value.map(item => item.FolderID);
  const newData = copiedData.filter(
    item => !existingIds.includes(item.CampaignID)
  );

  AdCampaignShowData.value.mMAdCampaigns = [
    ...AdCampaignShowData.value.mMAdCampaigns,
    ...newData
  ];
  CampaigndialogVisible.value = false;
  selectedRows.value = [];
};

// 点击新增文件夹按钮触发
const submitFolder = async () => {
  try {
    //新增操作
    const Addfolder = {
      Model: {
        FolderID: folderData.FolderID,
        FolderName: folderData.FolderName
      }
    };
    const response = await AddFolderAsyncFile(Addfolder);
    dialogFolderVisible.value = false;
    ElMessage.success(response.message);
    applyFilters(); // 初始加载后立即应用过滤
    await fetchFolderList();
  } catch {
    ElMessage.error("新建失败");
  }
};

// 获取文件夹列表
const fetchFolderList = async () => {
  const response = await getFolderAsync();
  folderList.value = response.data;
};

//搜索文件夹
const QuerySelectFolderBtn = () => {
  FolderShowData.value = folderList.value.filter(item =>
    item.FolderName.includes(searchFolderNameKeyword.value)
  );
};
// 获取素材列表
const fetchFile = async () => {
  loading.value = true;
  try {
    //编写参数对象
    const params = {
      Status: "1",
      AllDates: new Date(Date.now()),
      QueryCriteria: searchKeyword.value,
      Rows: pagination.Rows,
      Page: pagination.current,
      Sidx: "1",
      Sord: "1"
    };
    // 调用设备接口方法getAsyncFile，并将参数传入
    const response = await getAsyncFile(params);
    modelList.value = response.data.list.map(item => ({
      ...item,
      Movechecked: false // 为每个对象添加选中状态
    }));
    //pagination = response.data.paging;
    applyFilters(); // 初始加载后立即应用过滤
  } finally {
    loading.value = false;
  }
};

// 计算分页数据
const pagedData = computed(() => {
  const start = (pagination.current - 1) * pagination.Rows;
  return filteredData.value.slice(start, start + pagination.Rows);
});

const movebtn = () => {
  checkboxISShow.value = true;
  const cheModelList = modelList.value.filter(item => item.Movechecked); //获取所有选中的素材记录
  if (cheModelList.length > 0) {
    isMoveDisabled.value = false;
  } else {
    isMoveDisabled.value = true;
  }
};

//点击确认移动按钮的业务逻辑
const CheckMoveBtn = () => {
  const cheModelList = modelList.value.filter(item => item.Movechecked); //获取所有选中的素材记录
  console.log(cheModelList);
  FolderShowData.value = folderList.value;
  FolderdialogVisible.value = true;
};

//点击多选框按钮的业务逻辑
const handlecheckMoveChange = () => {
  const cheModelList = modelList.value.filter(item => item.Movechecked); //获取所有选中的素材记录
  if (cheModelList.length > 0) {
    isMoveDisabled.value = false;
  } else {
    isMoveDisabled.value = true;
  }
};

const CloseMoveBtn = () => {
  checkboxISShow.value = false;
  modelList.value = modelList.value.map(item => ({
    ...item,
    Movechecked: false // 为每个对象添加选中状态
  }));
  applyFilters();
};
// 显示对话框
const showDialog = (type, device) => {
  dialogVisible.value = true;
};
const handleChange = file => {
  fileList.value = [...fileList.value, file];
  // 自动更新 fileList（Element Plus 内部实现）
  console.log("新增文件:", file);
};

// 菜单点击事件
const handleFolderMenuSelect = async index => {
  searchKeyword.value = "";
  checkboxISShow.value = false;
  const menuId = parseInt(index);
  console.log("选择结果:", menuId);
  if (!isNaN(menuId)) {
    modelList.value = folderList.value
      .filter(item => item.FolderID == menuId)
      .flatMap(item =>
        item.mFileDtos.map(at => ({
          ...at,
          Movechecked: false // 为每个对象添加选中状态
        }))
      );
    applyFilters();
  }
};

// 应用过滤条件
const applyFilters = () => {
  filteredData.value = modelList.value.filter(item => {
    const keywordMatch = item.FileName?.includes(searchKeyword.value) || false;
    // const categoryMatch =
    //   !state.filters.category || item.category === state.filters.category;
    // const dateMatch =
    //   !state.filters.dateRange?.length ||
    //   (new Date(item.createTime) >= new Date(state.filters.dateRange[0]) &&
    //     new Date(item.createTime) <= new Date(state.filters.dateRange[1]));
    return keywordMatch;
  });
  console.log(pagination.Records);
  pagination.Records = filteredData.value.length;
  pagination.current = 1; // 重置页码
};

// const MenuHandleClose = (key: string, keyPath: string[]) => {
//   console.log(key, keyPath);
// };

// 上传素材
const FileUpload = async () => {
  if (fileList.value.length === 0) {
    console.error("文件列表为空");
    return;
  }
  const formData = new FormData();
  fileList.value.forEach(file => {
    var s = file.raw;
    formData.append("files", file.raw);
  });

  try {
    const response = await UploadAsyncFile(formData);
    //大于0 说明有错误
    if (response.data.FailedFiles.length > 0) {
      const errorMessage = response.data.FailedFiles.join("<br>");
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true // 显示关闭按钮
      });
      fileList.value = [];
    } else {
      ElMessage.success(response.data.message);
      dialogVisible.value = false;
      fileList.value = [];
      await fetchFile();
    }
  } catch {
    ElMessage.error("上传失败");
  }
};

// 删除禁用素材
const deleteFile = async id => {
  const deleteFile = {
    FileID: id
  };
  var response = await DeleteAsyncFile(deleteFile);
  if (response.state == 200) {
    await fetchFile();
    ElMessage.success(response.message);
  } else if (response.state == 400) {
    ElMessage.success(response.message);
  }
};

// 分页处理
const handlePaginationChange = () => {
  fetchFile();
};

// 日期格式化
const formatDate = date => {
  return new Date(date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 初始化加载
onMounted(() => {
  fetchFile();
  fetchFolderList();
});
</script>

<style scoped lang="scss">
.el-table .el-radio__label {
  display: none;
} /* 隐藏标签 */
.el-table .el-radio__inner {
  border-radius: 50%;
} /* 圆形单选框 */

.fixed-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff; /* 设置背景颜色为白色 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 1000; /* 确保横幅在其他元素之上 */
}
.checkbox-container {
  display: flex;
  justify-content: flex-end; /* 靠右对齐 */
}
.el-menu-item:hover {
  background-color: rgb(231, 235, 240) !important;
}
.el-menu-item.is-active {
  font-weight: 800 !important;
}
.upload-text {
  margin: 10px 0;
  color: #666;
  line-height: 1.5;
}

.preview-container {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.preview-item {
  height: 100px;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}
.media-card {
  margin-bottom: 20px;
  transition: transform 0.3s;
}

.media-card:hover {
  transform: translateY(-5px);
}

.media-content {
  width: 100%;
  /* 图片/视频通用样式 */
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
  background: #f5f7fa;
}

/* 音频播放器特殊样式 */
.audio-player {
  width: 100%;
  height: 60px;
  margin: 20px 0;
}

/* 响应式视频容器 */
.video-wrapper {
  position: relative;
  padding-top: 56.25%; /* 16:9 比例 */
}

.video-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.card-footer {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 14px;
  color: #606266;
}
.class-management {
  padding: 20px;
  background-color: #f5f7fa;

  .operation-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }
  }

  .class-list {
    margin-bottom: 20px;
    height: 60%;
  }

  .class-card {
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .class-header {
      display: flex;
      align-items: center;
      gap: 10px;

      .title-name {
        font-weight: bold;
        font-size: 16px;
      }
    }

    .card-content {
      padding: 10px 0;

      .info-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
          color: #666;
        }

        .label {
          color: #666;
          margin-right: 5px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;

    .search-input {
      width: 100% !important;
    }
  }
}
</style>
