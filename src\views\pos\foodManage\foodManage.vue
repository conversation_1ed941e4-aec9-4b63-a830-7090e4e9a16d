<template>
  <!-- 主页面 -->
  <div style="margin: 5px; max-height: calc(100vh -200px)">
    <el-tabs
      type="border-card"
      style="height: 100%; width: 100%"
      @tab-click="handleTabClick"
    >
      <el-tab-pane label="已发布">
        <el-input
          v-model="foodManageSearch"
          style="max-width: 300px"
          placeholder="请输入名称进行搜索"
          class="input-with-select"
          clearable
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch" />
          </template>
        </el-input>

        <pure-table
          :columns="publishColumns"
          :data="filteredPublishData"
          style="height: 69vh"
        >
          <!-- 操作栏插槽 -->
          <template #operation="{ row }">
            <el-button link type="primary" size="small" @click="resview(row)"
              >查看</el-button
            >
          </template>
        </pure-table>
      </el-tab-pane>
      <el-tab-pane label="草稿箱">
        <el-input
          v-model="foodManageSearch"
          style="max-width: 300px"
          placeholder="请输入名称进行搜索"
          class="input-with-select"
          clearable
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch" />
          </template>
        </el-input>
        <el-button style="float: right" @click="addFood">新增草稿</el-button>

        <pure-table
          :columns="draftColumns"
          :data="filteredDraftData"
          style="height: 69vh"
        >
          <template #isDeleted="{ row }">
            <el-switch
              v-model="row.IsDeleted"
              disabled
              :disabled="true"
              :active-value="false"
              :inactive-value="true"
              active-color="#409EFF"
              inactive-color="#FF4949"
            />
          </template>
          <!-- 操作栏插槽 -->
          <template #operation="{ row }">
            <el-button
              v-if="!row.IsDeleted"
              link
              type="primary"
              size="small"
              @click="showDeleteConfirm(row)"
              >删除</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="row.IsDeleted ? handleView(row) : handleEdit(row)"
            >
              {{ row.IsDeleted ? "查看" : "修改" }}
            </el-button>
          </template>
        </pure-table>
      </el-tab-pane>
      <el-pagination
        size="small"
        background
        layout="prev, pager, next"
        :total="pagination.Records"
        v-model:current-page="pagination.current"
        :page-size="20"
        class="mt-4"
        @current-change="handlePaginationChange"
      />
    </el-tabs>
    <!-- 弹窗页面   v-model:addFoodManageDialog值     addFoodManageDialog 为ture打开，false为关闭       -->
    <AddFoodManage
      v-model:addFoodManageDialog="addFoodManageDialog"
      :source="source"
    />
    <!-- <AdjustMent v-model:historyAdjustDialog="historyAdjustDialog"/> -->
  </div>
</template>
<script setup>
import { ref, watch, computed, onMounted } from "vue";
import AddFoodManage from "./addFoodManage.vue";
import AdjustMent from "./historyAdjustments.vue";
import { ElMessage, ElMessageBox } from "element-plus";
// 引入工具方法模块
import { utils } from "./modules/utils";
import {
  GetCommDraftRecord,
  GetCommPublished,
  GetCommDraftListRecord,
  DeleteCommDraftRecord
} from "@/api/foodManage";
import { Search } from "@element-plus/icons-vue";
const { isView, generateUniqueId } = utils();

const tabIndex = ref(0);
// 标签页点击事件
const handleTabClick = tab => {
  console.log(tab);
  if (tab.index === "0") {
    // 重置分页状态
    tabIndex.value = 0;
    pagination.current = 1;
    publishData();
  } else {
    tabIndex.value = 1;
    // 重置分页状态
    pagination.current = 1;
    draftData();
  }
};

// 新增按钮的弹窗
const addFoodManageDialog = ref(false);
// 定义表格列名称和配置信息
const publishColumns = [
  {
    label: "调价名称",
    prop: "Name",
    fixed: true
  },
  {
    label: "创建时间",
    prop: "CreationTime",
    slot: "CreationTime"
  },
  {
    label: "调价数量",
    prop: "DetailCount",
    slot: "DetailCount"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
];
// 定义草稿箱表格列名称和配置信息
const draftColumns = [
  {
    label: "调价名称",
    prop: "Name",
    fixed: true
  },
  {
    label: "创建时间",
    prop: "CreateTime",
    slot: "CreateTime"
  },
  {
    label: "调价数量",
    prop: "DetailCount",
    slot: "DetailCount"
  },
  {
    label: "删除状态",
    prop: "isDeleted",
    slot: "isDeleted"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
];

// 历史调价弹窗
const historyAdjustDialog = ref(false);
// 历史调价按钮点击事件
const historyAdjustment = () => {
  historyAdjustDialog.value = true;
};
// 点击新增按钮
const addFood = () => {
  source.value = "draft";
  isView.value = [];
  addFoodManageDialog.value = true;
};

const pagination = ref({
  Records: 0,
  Total: 0,
  current: 1
});
const reqBody = ref({
  "Paging.Rows": 20,
  "Paging.Page": 1,
  "Paging.Sidx": "1",
  "Paging.Sord": "1",
  "Paging.Records": 1,
  UserId: "USER001"
});
const reqView = ref({
  "Paging.Rows": 20,
  "Paging.Page": 1,
  "Paging.Sidx": "1",
  "Paging.Sord": "1",
  "Paging.Records": 1,
  UserId: "USER001"
});

const reqDelete = ref({
  UserId: "USER001"
});
const reqEdit = ref({
  "Paging.Rows": 20,
  "Paging.Page": 1,
  "Paging.Sidx": "1",
  "Paging.Sord": "1",
  "Paging.Records": 1,
  UserId: "USER001"
});
const source = ref("");
//查看
const resview = async row => {
  // source.value = tabIndex.value === 0 ? "published" : "draft";
  source.value = "published";
  console.log(row.AdjustmentId);

  const res = await GetCommPublished({
    ...reqView.value,
    AdjustmentId: row.AdjustmentId
  });
  isView.value = res.data.list;
  console.log(source);
  addFoodManageDialog.value = true;
};

// 删除确认弹窗
const showDeleteConfirm = row => {
  ElMessageBox.confirm("确定要删除这条记录吗？", "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      // 用户点击确定
      await handleDelete(row);
      ElMessage({
        type: "success",
        message: "删除成功"
      });
      // 删除成功后刷新数据
      if (tabIndex.value === 0) {
        await publishData();
      } else {
        await draftData();
      }
    })
    .catch(() => {
      // 用户点击取消
      ElMessage({
        type: "info",
        message: "已取消删除"
      });
    });
};

// 删除
const handleDelete = async row => {
  const res = await DeleteCommDraftRecord({
    ...reqDelete.value,
    AdjustmentId: row.AdjustmentID
  });
  return res;
};

//修改
const handleEdit = async row => {
  source.value = "draft";
  // reqEdit.value.AdjustmentId = row.AdjustmentId;
  console.log(row.AdjustmentID);
  const res = await GetCommDraftListRecord({
    ...reqEdit.value,
    AdjustmentID: row.AdjustmentID
  });
  isView.value = res.data.list.map(item => ({
    ...item,
    id: item.DetailID
  }));
  console.log(res);
  addFoodManageDialog.value = true;
};

// 查看草稿
const handleView = async row => {
  source.value = "published";
  const res = await GetCommDraftListRecord({
    ...reqEdit.value,
    AdjustmentID: row.AdjustmentID
  });
  isView.value = res.data.list.map(item => ({
    ...item,
    id: item.DetailID
  }));

  addFoodManageDialog.value = true;
};

const originalPublishData = ref([]);
const originalDraftData = ref([]);

// 获取已发布表格数据
const publishData = async () => {
  const res = await GetCommPublished(reqBody.value);
  // console.log(res);
  originalPublishData.value = res.data.list;
  console.log(res.data.list[0].AdjustmentId);
  pagination.value.Records = res.data.paging.Records;
};

// 获取草稿箱表格数据
const draftData = async () => {
  const res = await GetCommDraftRecord(reqBody.value);
  console.log(res);
  originalDraftData.value = res.data.list;
  pagination.value.Records = res.data.paging.Records;
};

// 计算属性 - 已发布数据的筛选结果
const filteredPublishData = computed(() => {
  return originalPublishData.value;
});

// 计算属性 - 草稿箱数据的筛选结果
const filteredDraftData = computed(() => {
  return originalDraftData.value;
});

// 在组件挂载后立即获取已发布数据
onMounted(() => {
  publishData();
});
// 搜索框按钮绑定数据
const foodManageSearch = ref("");

// 搜索按钮点击事件
const handleSearch = () => {
  reqBody.value.Keyword = foodManageSearch.value;
  pagination.current = 1;
  if (tabIndex.value === 0) {
    publishData();
  } else {
    draftData();
  }
};
// 防抖计时器
const searchTimer = ref(null);

// 修改后的搜索监听
watch(foodManageSearch, newVal => {
  clearTimeout(searchTimer.value);
  searchTimer.value = setTimeout(() => {
    reqBody.value.Keyword = newVal;
    pagination.value.current = 1;
    if (tabIndex.value === 0) {
      publishData();
    } else {
      draftData();
    }
  }, 500); // 500ms 防抖延迟
});
// 分页组件的当前页面变化事件处理函数
const handlePaginationChange = page => {
  reqBody.value["Paging.Page"] = page;
  if (tabIndex.value === 0) {
    publishData();
  } else {
    draftData();
  }
};

// 监听弹窗关闭
watch(addFoodManageDialog, newVal => {
  if (!newVal) {
    reqBody.value["Paging.Page"] = 1;
    // 如果弹窗关闭，重置表单数据
    if (tabIndex.value === 0) {
      publishData();
    } else {
      draftData();
    }
  }
});
</script>
