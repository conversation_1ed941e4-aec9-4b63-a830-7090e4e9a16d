<template>
  <header
    class="bg-white/95 backdrop-blur-md sticky top-0 z-50 border-b border-slate-200 shadow-sm"
  >
    <div class="container mx-auto px-4 sm:px-6">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-md"
            >
              <font-awesome-icon icon="desktop" class="text-white text-xl" />
            </div>
            <div>
              <h1 class="text-xl font-bold text-slate-800">KTV 收银系统</h1>
              <p class="text-xs text-slate-500">智能房间管理</p>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- 预订管理 -->
          <div class="relative" ref="reservationMenuRef">
            <!-- <button
              @click="toggleReservationMenu"
              class="flex items-center px-3 py-2 text-slate-600 hover:text-blue-600 bg-white hover:bg-white/90 rounded-lg transition-all duration-200 backdrop-blur-sm border border-slate-200 shadow-sm"
            >
              <font-awesome-icon icon="calendar-alt" class="mr-2" />
              预订管理
              <font-awesome-icon
                icon="chevron-down"
                :class="[
                  'ml-1 text-slate-400 transition-transform duration-200',
                  reservationMenuOpen ? 'rotate-180' : ''
                ]"
              />
            </button> -->

            <!-- 预订管理下拉菜单 -->
            <div
              v-if="reservationMenuOpen"
              class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50"
            >
              <button
                @click="handleReservationCommand('list')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="list" class="mr-2 text-slate-400" />
                预订列表
              </button>
              <button
                @click="handleReservationCommand('add')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="plus" class="mr-2 text-slate-400" />
                新增预订
              </button>
              <button
                @click="handleReservationCommand('calendar')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon
                  icon="calendar"
                  class="mr-2 text-slate-400"
                />
                预订日历
              </button>
            </div>
          </div>

          <!-- 顾客信息 -->
          <div class="relative" ref="customerMenuRef">
            <!-- <button
              @click="toggleCustomerMenu"
              class="flex items-center px-3 py-2 text-slate-600 hover:text-blue-600 bg-white hover:bg-white/90 rounded-lg transition-all duration-200 backdrop-blur-sm border border-slate-200 shadow-sm"
            >
              <font-awesome-icon icon="users" class="mr-2" />
              顾客信息
              <font-awesome-icon
                icon="chevron-down"
                :class="[
                  'ml-1 text-slate-400 transition-transform duration-200',
                  customerMenuOpen ? 'rotate-180' : ''
                ]"
              />
            </button> -->

            <!-- 顾客信息下拉菜单 -->
            <div
              v-if="customerMenuOpen"
              class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50"
            >
              <button
                @click="handleCustomerCommand('search')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="search" class="mr-2 text-slate-400" />
                查找顾客
              </button>
              <button
                @click="handleCustomerCommand('add')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon
                  icon="user-plus"
                  class="mr-2 text-slate-400"
                />
                新增顾客
              </button>
              <button
                @click="handleCustomerCommand('history')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="history" class="mr-2 text-slate-400" />
                消费记录
              </button>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="relative w-48">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <font-awesome-icon icon="search" class="text-slate-400" />
            </div>
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索房号..."
              class="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80"
              @input="handleSearch"
            />
            <button
              v-if="searchKeyword"
              @click="
                searchKeyword = '';
                handleSearch();
              "
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <font-awesome-icon
                icon="times"
                class="text-slate-400 hover:text-slate-600"
              />
            </button>
          </div>

          <!-- 返回系统选择 -->
          <button
            @click="backToSystemSelector"
            class="flex items-center px-3 py-2 text-slate-600 hover:text-blue-600 bg-white hover:bg-white/90 rounded-lg transition-all duration-200 backdrop-blur-sm"
          >
            <font-awesome-icon icon="arrow-left" class="mr-1" />
            返回
          </button>

          <!-- 用户菜单 -->
          <div class="relative" ref="userMenuRef">
            <button
              @click="toggleUserMenu"
              class="flex items-center space-x-2 cursor-pointer bg-white hover:bg-white/90 rounded-lg px-3 py-2 transition-colors duration-200 shadow-sm border border-slate-200"
            >
              <div
                class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"
              >
                <font-awesome-icon icon="user" class="text-white text-sm" />
              </div>
              <span class="text-slate-700 font-medium">{{
                currentUser.name
              }}</span>
              <font-awesome-icon
                icon="chevron-down"
                :class="[
                  'text-slate-400 transition-transform duration-200',
                  userMenuOpen ? 'rotate-180' : ''
                ]"
              />
            </button>

            <!-- 下拉菜单 -->
            <div
              v-if="userMenuOpen"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50"
            >
              <button
                @click="handleUserCommand('profile')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="user" class="mr-2 text-slate-400" />
                个人资料
              </button>
              <button
                @click="handleUserCommand('password')"
                class="w-full text-left px-4 py-2 text-sm text-slate-700 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="lock" class="mr-2 text-slate-400" />
                修改密码
              </button>
              <hr class="my-1 border-slate-200" />
              <button
                @click="handleUserCommand('logout')"
                class="w-full text-left px-4 py-2 text-sm text-red-600 bg-white hover:bg-gray-50 flex items-center"
              >
                <font-awesome-icon icon="power-off" class="mr-2 text-red-500" />
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- 预订管理弹窗 -->
  <ReservationManagement
    v-if="showReservationManagement"
    @close="showReservationManagement = false"
  />

  <!-- 顾客管理弹窗 -->
  <CustomerManagement
    v-if="showCustomerManagement"
    @close="showCustomerManagement = false"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { removeToken } from "@/utils/auth";
import { useUserStore } from "@/store/modules/user";
import ReservationManagement from "../reservation/reservation-management.vue";
import CustomerManagement from "../customer/customer-management.vue";

defineOptions({
  name: "CashierHeader"
});

// Props
interface Props {
  searchKeyword: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "update:searchKeyword": [value: string];
  search: [];
}>();

const router = useRouter();

const userStore = useUserStore();

// 当前用户信息
const currentUser = ref({
  name: userStore.nickname || userStore.username,
  role: "cashier"
});

// 用户菜单状态
const userMenuOpen = ref(false);
const userMenuRef = ref<HTMLElement>();

// 预订管理菜单状态
const reservationMenuOpen = ref(false);
const reservationMenuRef = ref<HTMLElement>();

// 顾客信息菜单状态
const customerMenuOpen = ref(false);
const customerMenuRef = ref<HTMLElement>();

// 搜索关键词的双向绑定
const searchKeyword = computed({
  get: () => props.searchKeyword,
  set: (value: string) => emit("update:searchKeyword", value)
});

// 处理搜索
const handleSearch = () => {
  emit("search");
};

// 返回系统选择
const backToSystemSelector = () => {
  router.push("/system-selector");
};

// 切换用户菜单
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value;
};

// 切换预订管理菜单
const toggleReservationMenu = () => {
  reservationMenuOpen.value = !reservationMenuOpen.value;
  // 关闭其他菜单
  customerMenuOpen.value = false;
  userMenuOpen.value = false;
};

// 切换顾客信息菜单
const toggleCustomerMenu = () => {
  customerMenuOpen.value = !customerMenuOpen.value;
  // 关闭其他菜单
  reservationMenuOpen.value = false;
  userMenuOpen.value = false;
};

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    userMenuOpen.value = false;
  }
  if (
    reservationMenuRef.value &&
    !reservationMenuRef.value.contains(event.target as Node)
  ) {
    reservationMenuOpen.value = false;
  }
  if (
    customerMenuRef.value &&
    !customerMenuRef.value.contains(event.target as Node)
  ) {
    customerMenuOpen.value = false;
  }
};

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  userMenuOpen.value = false; // 关闭菜单

  switch (command) {
    case "profile":
      ElMessage.info("个人资料功能开发中...");
      break;
    case "password":
      ElMessage.info("修改密码功能开发中...");
      break;
    case "logout":
      ElMessageBox.confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        removeToken();
        router.push("/login");
        ElMessage.success("已退出登录");
      });
      break;
  }
};

// 处理预订管理命令
const handleReservationCommand = (command: string) => {
  reservationMenuOpen.value = false; // 关闭菜单

  switch (command) {
    case "list":
      showReservationManagement.value = true;
      break;
    case "add":
      showReservationManagement.value = true;
      // TODO: 直接打开新增预订表单
      break;
    case "calendar":
      ElMessage.info("预订日历功能开发中...");
      // TODO: 打开预订日历弹窗
      break;
  }
};

// 处理顾客信息命令
const handleCustomerCommand = (command: string) => {
  customerMenuOpen.value = false; // 关闭菜单

  switch (command) {
    case "search":
      showCustomerManagement.value = true;
      break;
    case "add":
      showCustomerManagement.value = true;
      // TODO: 直接打开新增顾客表单
      break;
    case "history":
      showCustomerManagement.value = true;
      // TODO: 直接打开消费记录视图
      break;
  }
};

// 弹窗状态管理
const showReservationManagement = ref(false);
const showCustomerManagement = ref(false);

// 生命周期
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  console.log("当前用户信息:", userStore.username);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
