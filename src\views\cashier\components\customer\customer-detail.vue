<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl h-[80vh] flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="user" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">顾客详情</h2>
            <p class="text-sm text-slate-500">{{ customer?.name }} 的详细信息</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="flex-1 overflow-auto p-6">
        <div v-if="customer" class="space-y-6">
          <!-- 基本信息卡片 -->
          <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-slate-800 mb-4">基本信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm text-slate-600">姓名</label>
                <p class="font-medium text-slate-800">{{ customer.name }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">联系电话</label>
                <p class="font-medium text-slate-800">{{ customer.phone }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">电子邮箱</label>
                <p class="font-medium text-slate-800">{{ customer.email || '未填写' }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">性别</label>
                <p class="font-medium text-slate-800">{{ getGenderText(customer.gender) }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">生日</label>
                <p class="font-medium text-slate-800">{{ customer.birthday || '未填写' }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">会员等级</label>
                <span
                  :class="[
                    'inline-block px-2 py-1 rounded-full text-xs font-medium',
                    getLevelClass(customer.level)
                  ]"
                >
                  {{ getLevelText(customer.level) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 消费统计卡片 -->
          <div class="bg-white border border-slate-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-slate-800 mb-4">消费统计</h3>
            <div class="grid grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ customer.points }}</div>
                <div class="text-sm text-slate-600">积分余额</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">¥{{ customer.totalConsumption }}</div>
                <div class="text-sm text-slate-600">累计消费</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ customer.visitCount }}</div>
                <div class="text-sm text-slate-600">到店次数</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">
                  {{ customer.totalConsumption > 0 ? Math.round(customer.totalConsumption / customer.visitCount) : 0 }}
                </div>
                <div class="text-sm text-slate-600">平均消费</div>
              </div>
            </div>
          </div>

          <!-- 会员信息卡片 -->
          <div class="bg-white border border-slate-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-slate-800 mb-4">会员信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm text-slate-600">入会时间</label>
                <p class="font-medium text-slate-800">{{ formatDate(customer.membershipDate) }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">最后到店</label>
                <p class="font-medium text-slate-800">{{ customer.lastVisitDate ? formatDate(customer.lastVisitDate) : '未到店' }}</p>
              </div>
              <div>
                <label class="text-sm text-slate-600">账户状态</label>
                <span
                  :class="[
                    'inline-block px-2 py-1 rounded-full text-xs font-medium',
                    customer.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ customer.isActive ? '正常' : '已停用' }}
                </span>
              </div>
            </div>
            <div v-if="customer.notes" class="mt-4">
              <label class="text-sm text-slate-600">备注信息</label>
              <p class="font-medium text-slate-800 mt-1">{{ customer.notes }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-end space-x-3 p-6 border-t border-slate-200">
        <button
          @click="editCustomer"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          编辑信息
        </button>
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Customer, CustomerLevel, CustomerGender } from '../../types/customer';

defineOptions({
  name: 'CustomerDetail'
});

// Props
const props = defineProps<{
  customer: Customer | null;
}>();

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 方法
const getLevelClass = (level: CustomerLevel): string => {
  const classes = {
    bronze: 'bg-orange-100 text-orange-800',
    silver: 'bg-gray-100 text-gray-800',
    gold: 'bg-yellow-100 text-yellow-800',
    platinum: 'bg-blue-100 text-blue-800',
    diamond: 'bg-purple-100 text-purple-800'
  };
  return classes[level];
};

const getLevelText = (level: CustomerLevel): string => {
  const texts = {
    bronze: '青铜会员',
    silver: '白银会员',
    gold: '黄金会员',
    platinum: '铂金会员',
    diamond: '钻石会员'
  };
  return texts[level];
};

const getGenderText = (gender?: CustomerGender): string => {
  if (!gender) return '未填写';
  const texts = {
    male: '男',
    female: '女',
    other: '其他'
  };
  return texts[gender];
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const editCustomer = () => {
  console.log('编辑顾客信息:', props.customer);
  // TODO: 打开编辑表单
};
</script>
