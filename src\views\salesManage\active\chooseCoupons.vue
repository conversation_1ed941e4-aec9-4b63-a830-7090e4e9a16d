<script setup>
import { ref, toRefs, watch, nextTick } from "vue";
import { Search } from "@element-plus/icons-vue";
import { GetCardSheetListRecord } from "@/api/activeCoupon/activeCoupon";
import { getData } from "./modules/utils";
import { coupon } from "../coupons/modules/utils";
const { chooseCouponValue, couponTable } = getData();
const { couponValue } = coupon();
// 接收父组件的值
const props = defineProps({
  couponDialog: Boolean,
  couponState: String
});
const { couponDialog, couponState } = toRefs(props);
const emit = defineEmits(["update:couponDialog"]);
const columns = ref([
  {
    label: "选择",
    prop: "select",
    type: "selection",
    selectable: (row, index) => {
      return (
        couponState.value !== "coupon" ||
        multipleSelection.value.length === 0 ||
        multipleSelection.value.some(item => item.GrouponKey === row.GrouponKey)
      );
    }
  },
  {
    label: "卡券名称",
    prop: "GrouponName",
    slot: "GrouponName"
  },
  {
    label: "售价",
    prop: "GrouponPrice"
  },
  {
    label: "门市价",
    prop: "GoodsPrice"
  }
]);

const tableDataSortable = ref([]);
const dataLoad = ref(false);
const closeCoupon = () => {
  couponDialog.value = false;
  emit("update:couponDialog", false);
};

const tableRef = ref(null);
const multipleSelection = ref([]);
const allSelectedData = ref([]);
const handleSelectionChange = val => {
  console.log(val);
  if (couponState.value === "coupon") {
    console.log(val);
    // 单选模式：只保留最后一条选择
    if (val.length === 1) {
      const latestSelection = val[val.length - 1];
      // tableRef.value.clearSelection();
      // tableRef.value.toggleRowSelection(latestSelection, true);
      multipleSelection.value = [latestSelection];
      // 单选模式下自动确认选择
      sure();
    } else {
      multipleSelection.value = val;
    }
  } else {
    // 多选模式：正常处理多选
    multipleSelection.value = val;
  }
};
// 确认选择
const sure = () => {
  if (couponState.value === "sale") {
    // 多选模式：合并所有页面的选择
    const combined = [...multipleSelection.value, ...allSelectedData.value];
    // 根据 GrouponKey 去重
    chooseCouponValue.value = combined.filter(
      (item, index, self) =>
        index === self.findIndex(t => t.GrouponKey === item.GrouponKey)
    );
    allSelectedData.value = chooseCouponValue.value;
    closeCoupon();
  } else {
    // 单选模式：直接使用当前选择
    couponValue.value =
      multipleSelection.value.length > 0 ? [multipleSelection.value[0]] : [];
    closeCoupon();
  }
};
// 关于分页
const page = ref({
  current: 1,
  size: 20,
  total: 0
});
// 搜索绑定数据
const req = ref({
  CardSheetListName: "",
  CardSheetListId: "",
  "Paging.Rows": 20,
  "Paging.Page": "1",
  "Paging.Sidx": "InputTime",
  "Paging.Sord": "desc",
  "Paging.Records": "1"
});

// 选择/取消选择行
const handlePaginChange = async val => {
  //
  // 保存当前页面的选择
  if (couponState.value === "sale" && multipleSelection.value.length > 0) {
    const currentPageSelected = multipleSelection.value.filter(item =>
      tableDataSortable.value.some(row => row.GrouponKey === item.GrouponKey)
    );
    allSelectedData.value = [
      ...allSelectedData.value.filter(
        item =>
          !tableDataSortable.value.some(
            row => row.GrouponKey === item.GrouponKey
          )
      ),
      ...currentPageSelected
    ];
    console.log(allSelectedData.value);
  }

  req.value["Paging.Page"] = val;
  page.value.current = val;
  await getCouponsData();

  // 恢复当前页面的选择
  if (couponState.value === "sale") {
    nextTick(() => {
      tableDataSortable.value.forEach(row => {
        const isSelected = allSelectedData.value.some(
          item => item.GrouponKey === row.GrouponKey
        );
        tableRef.value.getTableRef().toggleRowSelection(row, isSelected);
      });
    });
  }
};

const getCouponsData = async () => {
  dataLoad.value = true;
  if (couponState.value === "sale") {
    multipleSelection.value = [];
  }
  const res = await GetCardSheetListRecord(req.value);
  console.log(res.data);
  page.value.total = res.data.paging.Records;
  tableDataSortable.value = res.data.list;
  // 恢复当前页面的选择状态
  if (couponState.value === "sale") {
    nextTick(() => {
      tableDataSortable.value.forEach(row => {
        const isSelected = allSelectedData.value.some(
          item => item.GrouponKey === row.GrouponKey
        );
        tableRef.value.getTableRef().toggleRowSelection(row, isSelected);
      });
    });
  }
  dataLoad.value = false;
};
// 搜索卡券
const searchKeyword = ref("");
const searchData = async () => {
  req.value["Paging.Page"] = 1;
  page.value.current = 1;
  req.value.CardSheetListName = searchKeyword.value;
  await getCouponsData();
};
// 监听弹窗打开事件
watch(
  () => couponDialog.value,
  async val => {
    if (val) {
      await getCouponsData();
      console.log(tableDataSortable.value);
      allSelectedData.value = couponTable.value;
      // multipleSelection.value = [];
      // allSelectedData.value = [];
      // if (tableRef.value?.getTableRef()) {
      //   tableRef.value.getTableRef().clearSelection();
      // }
    }
  }
);
// 监听单选/多选模式变化
watch(couponState, () => {
  multipleSelection.value = [];
  allSelectedData.value = [];
  if (tableRef.value?.getTableRef()) {
    tableRef.value.getTableRef().clearSelection();
  }
});
</script>

<template>
  <div>
    <el-dialog
      v-model="couponDialog"
      title="选择卡券"
      width="70%"
      style="height: 100%; margin: 0px auto"
      draggable
      @close="closeCoupon()"
    >
      <el-input
        v-model="searchKeyword"
        placeholder="搜索名称"
        clearable
        class="search-input"
        style="width: 200px; margin-right: 10px"
      >
      </el-input>
      <el-button type="primary" @click="searchData">
        <el-icon><Search /></el-icon>
        查询
      </el-button>

      <!-- 统一使用一个表格 -->
      <pure-table
        ref="tableRef"
        :data="tableDataSortable"
        height="70vh"
        :columns="columns"
        v-loading="dataLoad"
        @selection-change="handleSelectionChange"
      />

      <el-pagination
        v-model:current-page="page.current"
        v-model:page-size="page.size"
        :total="page.total"
        layout="prev, pager, next, jumper"
        @current-change="handlePaginChange"
        style="margin: 10px 20px 0px 20px"
      />

      <div class="dialog-footer" style="float: right">
        <el-button @click="closeCoupon()">取消</el-button>
        <!-- 多选模式才显示选择按钮 -->
        <el-button type="primary" @click="sure()" v-if="couponState === 'sale'">
          选择
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>
