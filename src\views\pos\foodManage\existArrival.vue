<template>
  <!-- 选择已有商品 -->
  <div>
    <el-dialog
      v-model="existArrivalDialog"
      draggable
      fullscreen
      @close="existArrivalDialogClose"
      width="800px"
      top="0"
      title="选择已有商品"
      stripe="true"
    >
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item>
            <el-input
              v-model="search.Keyword"
              placeholder="商品/套餐的名称、编号"
              clearable
              @keyup.enter="searchArrivalData"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-select v-model="search.FtNo" placeholder="按类别筛选" clearable>
              <el-option label="饮品" value="1" />
              <el-option label="甜点" value="2" />
              <el-option label="主食" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3" v-if="!forceCommodityMode">
          <el-form-item label="只查询套餐">
            <el-switch v-model="search.IsPackage" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="searchArrivalData">搜索</el-button>
          <el-button @click="Reset" style="margin-left: 15px">重置</el-button>
        </el-col>
      </el-row>
      <el-table
        :data="tableData"
        style="width: 100%"
        max-height="69vh"
        size="default"
        v-loading="tableLoad"
        @selection-change="existArrivalSelection"
      >
        <el-table-column type="selection" width="55" fixed />
        <el-table-column
          prop="IsPackage"
          label="是否套餐"
          :formatter="formatIsPackage"
          fixed
        />
        <el-table-column
          label="商品编号："
          width="120"
          prop="FdNo"
          :formatter="row => (row.FdNo ? row.FdNo : '-')"
        >
        </el-table-column>
        <el-table-column
          label="商品名称："
          width="120"
          prop="FdCName"
          :formatter="row => (row.FdCName ? row.FdCName : '-')"
        >
        </el-table-column>
        <el-table-column
          label="套餐编号："
          width="120"
          prop="ComboNo"
          :formatter="row => (row.ComboNo ? row.ComboNo : '-')"
        >
        </el-table-column>
        <el-table-column
          prop="ComboName"
          label="套餐名称"
          width="120"
          show-overflow-tooltip
          :formatter="row => (row.ComboName ? row.ComboName : '-')"
        >
        </el-table-column>
        <el-table-column label="套餐内容" width="150">
          <template #default="{ row }">
            <div v-if="row.PackageItems">
              <div v-for="(item, index) in row.PackageItems" :key="index">
                {{ item.FdCName }}(x{{ item.FdQty }})
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="FtCName"
          label="类别名称"
          width="120"
          :formatter="row => (row.FtCName ? row.FtCName : '-')"
        ></el-table-column>
        <el-table-column prop="FdQty" label="商品数量" />
        <el-table-column prop="CreateTime" label="创建时间" width="200" />
        <el-table-column prop="UpdateTime" label="更新时间" width="200" />
      </el-table>
      <el-pagination
        size="small"
        background
        layout="prev, pager, next"
        :total="pagination.Records"
        v-model:current-page="pagination.current"
        class="mt-4"
        @current-change="handlePaginationChange"
      />
      <template #footer>
        <div class="dialog-footer button-container" style="margin: 0px">
          <el-button
            type="primary"
            style="width: 120px"
            @click="chooseExistArrival"
            >选择</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, toRefs, watch } from "vue";
import {
  GetCommExistingGoods,
  GetCommHistoricalPrice
} from "@/api/addFoodManage";
import { utils } from "./modules/utils";

const { addFoodManangeTable, generateUniqueId, isView, filteredData, isAdd } =
  utils();
const props = defineProps({
  existArrivalDialog: Boolean,
  search: Object,
  forceCommodityMode: {
    // 新增：商品专用模式
    type: Boolean,
    default: true
  },
  source: {
    type: String,
    default: "" // 'addFoodManage' 或 'newArrival'
  }
});
// 解构
const { existArrivalDialog, forceCommodityMode } = toRefs(props);

// 关闭事件 传递给父组件
const emit = defineEmits([
  "update:existArrivalDialog",
  "refreshParentData",
  "selected-data"
]);
const existArrivalDialogClose = () => {
  tableLoad.value = false;
  existArrivalDialog.value = false;
  emit("update:existArrivalDialog", false);
};
const tableLoad = ref(false);
const tableData = ref();

const formatIsPackage = row => {
  return row.IsPackage ? "是" : "否";
};

// 获取商品数据方法
const getArrivalData = req => {
  tableLoad.value = true;
  GetCommExistingGoods(req).then(res => {
    tableLoad.value = false;
    tableData.value = res.data.list;
    pagination.value.Records = res.data.paging.Records;
    pagination.value.Total = res.data.paging.Total;
    pagination.value.current = 1;
    console.log(res);
  });
};
// 分页数据绑定
const pagination = ref({
  Records: 0,
  Total: 0,
  current: 1
});
// 请求参数
const reqBody = ref({
  "Paging.Rows": 200,
  "Paging.Page": pagination.value.current,
  "Paging.Sidx": "1",
  "Paging.Sord": "1",
  "Paging.Records": 1
});
// 搜索框数据绑定
const search = ref({
  Keyword: "",
  FtNo: "",
  IsPackage: forceCommodityMode.value ? false : false
});
// 搜索按钮事件方法
const searchArrivalData = () => {
  const baseParams = {
    ...reqBody.value,
    Keyword: search.value.Keyword,
    FtNo: search.value.FtNo,
    // 关键逻辑：根据 forceCommodityMode 和开关状态决定查询条件
    IsPackage: search.value.IsPackage
  };

  console.log("最终请求参数:", baseParams);
  getArrivalData(baseParams);
};
watch(
  () => search.value,
  (newVal, oldVal) => {
    // 可以添加一些条件判断是否需要触发搜索
    searchArrivalData();
  },
  { deep: true }
);

watch(
  () => existArrivalDialog.value,
  newVal => {
    if (newVal === true) {
      // 确保初始加载时应用 forceCommodityMode 条件
      const req = {
        ...reqBody.value,
        Keyword: search.value.Keyword,
        FtNo: search.value.FtNo,
        // 关键：强制设置 IsPackage
        IsPackage: forceCommodityMode.value ? false : null
      };
      console.log(req);
      getArrivalData(req);
    }
  }
);

const arrivalTableSelection = ref([]);
// 表格选中数据
const existArrivalSelection = rows => {
  arrivalTableSelection.value = rows;
  console.log(arrivalTableSelection.value);
};

// 选择按钮加载状态
const chooseBtnLoad = ref(false);
// 点击选择按钮后的方法
const chooseExistArrival = () => {
  chooseBtnLoad.value = true;
  const result = arrivalTableSelection.value.map(data => {
    console.log(data);
    const cover = convertToResultFormat(data);
    return cover;
  }); // 打印或处理结果
  console.log("最终结果:", result);

  const { fdNo, comboNo } = arrivalTableSelection.value.reduce(
    (acc, item) => {
      if (item.Type === "1" && item.FdNo) acc.fdNo.push(item.FdNo);
      if (item.Type === "2" && item.ComboNo) acc.comboNo.push(item.ComboNo);
      return acc;
    },
    { fdNo: [], comboNo: [] }
  );

  const fdNos = fdNo.join(",");
  const comboNos = comboNo.join(",");
  console.log(fdNos); // 输出格式示例: "FD0010,FD001"
  console.log(comboNos); // 输出格式示例: "CB002"
  const reqUserbody = {
    ...reqBody.value,
    ComboNo: comboNos ? comboNos : "",
    FdNo: fdNos ? fdNos : "", // 注意参数名需与后端接口一致（如 FdNos 或 FdNo）
    UserId: "USER001"
  };
  const reqAllbody = {
    ...reqBody.value,
    ComboNo: comboNos ? comboNos : "",
    FdNo: fdNos ? fdNos : "", // 注意参数名需与后端接口一致（如 FdNos 或 FdNo）
    UserId: ""
  };
  // console.log(reqbody);
  // const Prices = ref([]);

  if (props.source === "addFoodManage") {
    GetCommHistoricalPrice(reqUserbody).then(res => {
      console.log(res);
      const allValues = res.data.list.map(item => {
        // 确保 Value 数组存在且至少有一个元素
        if (item.Value && item.Value.length > 0) {
          console.log(item.Value[0].SalePrice);
          return item.Value[0];
        }
      });
      console.log(allValues); // 输出所有 Value[0] 的数组
      console.log(...result);
      filteredData.value.unshift(...result);
      existArrivalDialogClose();
    });
  } else if (props.source === "newArrival") {
    GetCommHistoricalPrice(reqAllbody).then(res => {
      console.log(res);

      const priceMap = {};
      res.data.list.forEach(item => {
        console.log(item.Value);
        if (item.Value && item.Value.length > 0) {
          priceMap[item.Value[0].FdNo] = item.Value[0].SalePrice;
        } else {
          priceMap[item.Value[0].FdNo] = null; // 或者你可以设置一个默认价格
        }
      });
      result.forEach(item => {
        item.SalePrice = priceMap[item.FdNo] || null; // 使用商品ID查找对应价格
      });
      console.log(priceMap);
      // const Prices = res.data.list.map(item => {
      //   if (item.Value && item.Value.length > 0) {
      //     return item.Value[0].SalePrice;
      //   }
      //   return null; // 如果没有价格，返回 null（可替换为默认值）
      // });

      // console.log("所有价格:", Prices);

      // // 6. 将价格附加到 result 中的每一项（可选）
      // result.forEach((item, index) => {
      //   item.SalePrice = Prices[index];
      // });
      // console.log(res);
      console.log(result);
      emit("select-product", result); // 触发事件传给套餐
      existArrivalDialogClose();
    });
  }
};

// 发送批量请求
//   GetCommHistoricalPrice(reqbody).then(res => {
//     console.log(res);
//     // 处理返回结果
//     const Nos = `${comboNos},${fdNos}`;
//     value = filterAndGroupByKey(res.data, Nos);
//     console.log("筛选结果:", value);
//     chooseBtnLoad.value = false;
//     // 步骤 1：处理下层数据，统一为以 FdNo/ComboNo 为键的映射
//     const lowerMap = Object.entries(value).reduce((acc, [key, value]) => {
//       // 确定键类型：若包含 FD/CP 等前缀，判断为 FdNo 或 ComboNo
//       const isFdNo = key.startsWith("FD"); // 假设 FD 开头为单品，其他为套餐
//       acc[key] = { ...value, keyType: isFdNo ? "FdNo" : "ComboNo" };
//       return acc;
//     }, {});

//     // 步骤 2：合并上层数据和下层数据
//     mergedData = result.map(upperItem => {
//       const mergeKey = upperItem.FdNo || upperItem.ComboNo; // 优先使用 FdNo，若无则用 ComboNo
//       const lowerItem = lowerMap[mergeKey]; // 获取下层匹配项

//       if (!lowerItem) {
//         // 无匹配项，直接返回上层数据
//         return { ...upperItem };
//       }

//       // 合并字段，区分新旧值
//       return {
//         ...upperItem,
//         // 示例：合并 Name 字段，新值在前，旧值在后
//         Name: `新:${upperItem.Name}原:${lowerItem.Name}`,
//         // 合并 FdCName（若上层有值，下层有值则拼接）
//         FdCName: upperItem.FdCName
//           ? `新:${upperItem.FdCName}原:${lowerItem.FdCName}`
//           : "",
//         // 保留下层的 FdNo/ComboNo（确保键正确）
//         FdNo: upperItem.FdNo ? `新:${upperItem.FdNo}原:${lowerItem.FdNo}` : "",
//         ComboNo: upperItem.ComboNo
//           ? `新:${upperItem.ComboNo}原:${lowerItem.ComboNo}`
//           : "",
//         ComboName: upperItem.packageItems
//           ? `新:${upperItem.ComboName}原:${lowerItem.ComboName}`
//           : "",
//         // 其他字段：若上层有值则保留，否则取下层值
//         MarketPrice: `新:${upperItem.MarketPrice}原:${lowerItem.MarketPricee}`,
//         SalePrice: `新:${upperItem.SalePrice}原:${lowerItem.SalePrice}`,
//         // 处理特殊字段（如下层的 ComboItems）
//         ComboItems:
//           lowerItem.ComboItems || upperItem.packageItems?.toString() || "",
//         Type: upperItem.packageItems ? "2" : "1"
//         // 更多字段合并逻辑...
//       };
//     });

//     return mergedData;
//   });

// // 筛选数据方法
// const filterAndGroupByKey = (data, targetKeys) => {
//   const targetKeySet = new Set(targetKeys.split(",")); // 转换为 Set 便于快速查找
//   // 1. 筛选匹配的项并提取 Value
//   const allValues = data.list
//     .filter(item => {
//       // 从 Key 中提取 FdNo 或 ComboNo（假设 Key 格式为 "单品-xxx(FDxxx)" 或 "套餐-xxx(CPxxx)"）
//       const keyMatch = item.Key.match(/\(([^)]+)\)/);
//       const keyFromData = keyMatch?.[1];
//       return keyFromData && targetKeySet.has(keyFromData); // 检查是否在目标键集合中
//     })
//     .flatMap(item => item.Value);

//   // 2. 按数据类型（FdNo/ComboNo）分组
//   const grouped = allValues.reduce((acc, item) => {
//     const groupKey = item.ComboNo || item.FdNo; // 优先使用 ComboNo，其次 FdNo
//     if (!groupKey) return acc; // 跳过无分组键的数据
//     if (!acc[groupKey]) {
//       acc[groupKey] = {
//         groupKey, // 记录分组键（FdNo/ComboNo）
//         records: []
//       };
//     }
//     acc[groupKey].records.push(item);
//     return acc;
//   }, {});

//   // 3. 合并每个分组的数据
//   const mergedResult = Object.values(grouped).reduce((acc, group) => {
//     const { groupKey, records } = group;
//     const allFields = new Set();

//     // 收集所有字段（排除分组键本身）
//     records.forEach(record => {
//       Object.keys(record).forEach(key => {
//         if (key !== "FdNo" && key !== "ComboNo") {
//           // 排除分组键字段
//           allFields.add(key);
//         }
//       });
//     });

//     // 合并字段值
//     const mergedFields = [...allFields].reduce(
//       (merged, field) => {
//         const values = records
//           .map(record => record[field])
//           .filter(val => val !== null && val !== undefined)
//           .map(String);

//         const uniqueValues = [...new Set(values)];
//         merged[field] = uniqueValues.join(",");
//         return merged;
//       },
//       {
//         // 保留分组键（FdNo 或 ComboNo）
//         ...(groupKey.startsWith("FD")
//           ? { FdNo: groupKey }
//           : { ComboNo: groupKey })
//       }
//     );

//     acc[groupKey] = mergedFields;
//     return acc;
//   }, {});

//   console.log("合并结果:", mergedResult);
//   return mergedResult;
// };

// 数据处理方法
const convertToResultFormat = data => {
  const baseResult = {
    id: generateUniqueId(),
    Name: "",
    FdCName: data.FdCName,
    ComboName: data.ComboName,
    ComboNo: data.ComboNo,
    FdNo: data.FdNo,
    FtNo: data.FtNo,
    MarketPrice: data.MarketPrice || "0", // 示例值，需从 data 提取
    SalePrice: data.SalePrice || "0",
    PriceMode: data.PriceMode || "",
    delivery: data.PackageItems ? true : false,
    ApplicableStores: data.ApplicableStores || "",
    Type: data.PackageItems ? "2" : "1"
  };

  if (data.IsPackage && Array.isArray(data.PackageItems)) {
    baseResult.PackageItems = data.PackageItems.map(item => ({
      PGFdCName: item.FdCName,
      CommodityPrice: item.FdPrice || "0" // 假设 FdPrice 是子项价格字段
    }));
  }
  return baseResult;
};

// 搜索状态重置
const Reset = () => {
  if (forceCommodityMode.value == true) {
    reqBody.value.IsPackage = false;
    getArrivalData(reqBody.value);

    search.value = {
      Keyword: "",
      FtNo: "",
      IsPackage: false
    };
  } else {
    reqBody.value.IsPackage = null;
    getArrivalData(reqBody.value);

    search.value = {
      Keyword: "",
      FtNo: "",
      IsPackage: null
    };
  }
};
// 分页方法
const handlePaginationChange = page => {
  pagination.value.current = page;
  getArrivalData(reqBody.value);
};
</script>
<style></style>
