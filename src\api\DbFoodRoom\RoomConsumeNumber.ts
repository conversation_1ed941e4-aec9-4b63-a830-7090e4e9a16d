import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
import { baseUrlApi } from "../util";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//房间消费人数统计接口
//查询全部信息的接口
export const getAsyncDbFoodRoom = (params) => {
  return http.request<any>("get", "/api/DbFoodRoom/GetAll",{params});
};

//查询门店全部区域信息的接口
export const getAsyncDbFoodRoomRegion = (params) => {
  return http.request<any>("get", "/api/DbFoodRoom/GetRegionAll",{params});
};
