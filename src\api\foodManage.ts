import { http} from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */

// 查看已发布
export const GetCommPublished = (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommPublished", { params },
  );
};
// 查看草稿箱
export const GetCommDraftRecord= (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommDraftRecord", { params },
  );
};
// 查看草稿箱记录详情
export const GetCommDraftListRecord= (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommDraftListRecord", { params },
  );
};
// 草稿箱记录删除
export const DeleteCommDraftRecord= (data?: object) => {
  return http.request<any>("post", "/CommodityAdjustment/DeleteCommDraftRecord", { data },
  );
 
};


// 首页

