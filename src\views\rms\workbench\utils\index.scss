.grid-content {
  font-size: 13px;
  height: 32px;
  /* 固定高度，确保垂直居中生效 */
  display: flex;
  /* 开启弹性布局 */
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中（可选，根据需求添加） */
}

.main-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-right {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 10px;
  justify-content: flex-end;
}

.border-title {
  text-align: center;
  margin-top: -16px;
  width: 80px;
  background-color: white;
  color: rgb(90, 90, 90);
  margin-left: auto;
  margin-right: auto;
  font-size: 13px;
  /* 撑满容器高度 */
}

.havepeople {
  display: inline-block;
  margin: 13px 0px;
  padding: 5px;
  border: 1px solid rgb(192, 192, 192);

  span {
    margin: 0px 5px;
    padding: 5px;
  }
}

.havespecial {
  display: inline-block;
  margin:0px 0px;
  padding: 5px;
  font-size: 14px;
  div{
    padding:0px 5px;
  }
}

.ep-bg-orange {
  background-color: plum;
  /* border: 1px solid black; */
}

.ep-bg-purple {
  background-color: antiquewhite;
  /* border: 1px solid black; */
}

.ep-bg-wechat {
  background-color: #5dd497;
}

.ep-bg-birth {
  background-color: #f8c981;
}

.ep-bg-white {
  background-color: white;
  /* border: 1px solid black; */
}

.floor-container {
  padding: 5px;
  max-height: calc(100vh - 68px - 34px - 66px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.floor-wrapper {
  margin-bottom: 0px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  background-color: #f9f9f9;
  justify-content: flex-start;
  display: flex;
  /* 开启弹性布局 */
  flex-wrap: wrap;
  /* 自动换行 */
}

.floor-title {
  padding: 1px;
  background-color: #3c9cfd;
  color: white;
  font-weight: bold;
  margin: 0;
  width: 100%;
  height: 20px;
  font-size: 12px;
}

.floor-number {
  margin-right: 10px;
}

.room-item {
  padding: 8px;
  margin-bottom: 6px;
}

.room-number {
  font-size: 14px;
  font-weight: bold;
}

.room-status {
  font-size: 14px;
}

.room-flexbox {
  display: flex;
  /* 弹性布局 */
  flex-wrap: wrap;
  /* 自动换行 */
  padding: 5px;
  flex-direction: row;
  gap: 5px;
  /* 房间间距 */
  /* 关键：根据屏幕宽度自动调整列宽 */
  justify-content: flex-start;
}

.room-item {
  /* 基础尺寸 */
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  transition: flex-basis 0.3s ease;
  /* 平滑过渡 */
}

@media (max-width:992px) {
  .room-item {
    width: 60px;
    /* 比大屏稍小，适应窄屏桌面 */
    height: 40px;
  }
}
@media (min-width: 1680px) {
  .room-item {
      width: 60px;
      height:60px;
    }
}
/*>=1280的设备*/
/* 优化：屏幕宽度变化时调整房间宽度 */
@media (max-width: 1680px) {
  .room-item {
      width: 54px;
      /* 比大屏稍小，适应窄屏桌面 */
      height: 45px;
    }
    .room-number {
          font-size: 12px;
          font-weight: bold;
        }
    
        .room-status {
          font-size: 12px;
        }
}

@media (max-width: 768px) {
  .room-item {
    min-width: 60px;
    font-size: 12px;
    height: 40px;
  }
}
.icon-container {
  display: flex;
  justify-content: center;

}
.room-icon {
  display: flex;
  /* 行内块元素，避免换行 */
  vertical-align: top;
  /* 垂直对齐顶部，避免基线偏移 */
  align-items: center;
  justify-content: center;
}
.bg-available {
  background-color: antiquewhite;
}

.bg-occupied {
  background-color: rgb(207, 207, 207);
}

.leftBox {
  height: 100%;
  width: 350px;
  border: 1px solid rgb(133, 131, 131);
}

.main-content {
  height: 100%;
  background-color: white;
  transition: margin-right 0.3s ease;
  /* 关键：过渡动画 */
}

/* 右侧侧边栏 */
.side-drawer {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: 400px;
  /* 侧边栏宽度 */
  background-color: #f8f9fa;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  /* 初始隐藏在右侧 */
  transition: transform 0.3s ease;
  z-index: 1000;
}

/* 展开状态：侧边栏滑入，主内容添加 margin-right */
.side-drawer.is-open {
  transform: translateX(0);
}

.side-drawer.is-open~.main-container .main-content {
  margin-right: 300px;
  /* 主内容向右 margin，腾出侧边栏空间 */
}

/* 侧边栏内容样式 */
.drawer-header {
  padding: 16px;
  border-bottom: 1px solid #e9e9e9;
  display: flex;
}

.drawer-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.tableTotal {
  font-weight: 600;
  font-size: 12px;
  margin-right: 10px;
  margin-top: 10px;
}

.msg {
  font-size: 15px;
  font-weight: 600;
  border-left: 5px solid orange;
  padding-left: 10px;
}

.bookmsg {
  font-size: 13px;
  margin-top: 20px;
}

.msgtime {
  padding: 10px;
  font-size: 13px;
}

.t-g {
  color: rgb(90, 90, 90);
  margin-right: 10px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中（可选） */
  padding: 20px;
  /* 可选：添加容器内边距 */
}

/*flex*/
.flex_box {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.flex_column {
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.flex1 {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  /*width: 0%;*/
}

.flex2 {
  -webkit-box-flex: 2;
  -webkit-flex: 2;
  flex: 2;
}

.flex3 {
  -webkit-box-flex: 3;
  -webkit-flex: 3;
  flex: 3;
}

.flex4 {
  -webkit-box-flex: 4;
  -webkit-flex: 4;
  flex: 4;
}

.no_flex {
  flex: none;
}

.align_center {
  -webkit-box-align: center;
  -webkit-box-pack: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.align_end {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}

.j_center {
  justify-content: center;
}