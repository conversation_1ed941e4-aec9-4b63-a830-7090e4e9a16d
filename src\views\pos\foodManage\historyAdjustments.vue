<template>
  <!-- 历史调价记录 -->
  <div>
    <el-dialog
      v-model="historyAdjustDialog"
      fullscreen
      draggable
      @close="historyAdjustClose"
    >
      <el-input
        v-model="foodManageSearch"
        style="max-width: 300px; margin-left: 15px"
        placeholder="请输入关键字进行搜索"
        class="input-with-select"
        clearable
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <pure-table
        :columns="columns"
        :data="filteredData"
        style="height: 75vh; margin-top: 10px"
        v-loading="addTableLoad"
        alignWhole="center"
        border
      >
        <!-- 套餐详情插槽 -->
        <template #ComboItems="{ row }">
          <div v-if="row.ComboItems && row.ComboItems.length > 0">
            <!-- 遍历套餐子项 -->
            <div v-for="(item, index) in row.ComboItems" :key="index">
              {{ item.PGFdCName }}×{{ item.PGFdQty }}
              <span class="price">￥{{ item.CommodityPrice }}</span>
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </pure-table>
      <template #footer>
        <div class="dialog-footer button-container" style="padding: 0px">
          <el-button @click="historyAdjustClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, toRefs, watch } from "vue";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  historyAdjustDialog: Boolean,
  rowId: Object,
  historyData: {
    // 新增 prop 接收历史数据
    type: Array,
    default: () => []
  }
});

// 解构
const { historyAdjustDialog, historyData } = toRefs(props);

// 传递给父组件
const emit = defineEmits(["update:historyAdjustDialog"]);

// 弹窗关闭事件
const historyAdjustClose = () => {
  emit("update:historyAdjustDialog", false);
};
watch(
  () => props.historyData,
  newVal => {
    console.log("接收到历史数据:", newVal);
  },
  { immediate: true }
);

// 搜索功能
const foodManageSearch = ref("");
const filteredData = computed(() => {
  if (!foodManageSearch.value) return props.historyData;

  const searchTerm = foodManageSearch.value.toLowerCase();
  return props.historyData.filter(item => {
    return (
      (item.FdCName && item.FdCName.toLowerCase().includes(searchTerm)) ||
      (item.ComboName && item.ComboName.toLowerCase().includes(searchTerm))
    );
  });
});

// 表格数据加载状态
const addTableLoad = ref(false);

// 定义表格表头
const columns = [
  {
    label: "商品名称",
    prop: "FdCName",
    formatter: row => (row.FdCName ? row.FdCName : "-")
  },

  {
    label: "商品数量",
    prop: "FdQty",
    width: "90",
    formatter: row => (row.FdQty ? row.FdQty : "1")
  },
  {
    label: "套餐名称",
    prop: "ComboName",
    formatter: row => (row.ComboName ? row.ComboName : "-")
  },

  {
    label: "套餐详情",
    prop: "ComboItems",
    slot: "ComboItems"
  },
  {
    label: "市场价",
    prop: "MarketPrice",
    width: "90",
    formatter: row => row.MarketPrice + "元"
  },
  {
    label: "实际售价",
    prop: "SalePrice",
    width: "90",
    formatter: row => row.SalePrice + "元"
  },

  {
    label: "可用时间",
    prop: "PriceMode",
    width: "150"
  },
  {
    label: "创建时间",
    prop: "CreationTime",
    width: "190"
  }
];
</script>

<style>
.button-container {
  display: flex;
  justify-content: center;
  gap: 8px;
}
</style>
