<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="plus" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">新增预订</h2>
            <p class="text-sm text-slate-500">创建新的房间预订</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 表单内容 -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- 顾客信息 -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              顾客姓名 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.customerName"
              type="text"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入顾客姓名"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              联系电话 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.customerPhone"
              type="tel"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入联系电话"
            />
          </div>
        </div>

        <!-- 房间信息 -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              选择房间 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.roomId"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择房间</option>
              <option value="V01">V01 - VIP包厢</option>
              <option value="V02">V02 - VIP包厢</option>
              <option value="M01">M01 - 中包厢</option>
              <option value="M02">M02 - 中包厢</option>
              <option value="S01">S01 - 小包厢</option>
              <option value="S02">S02 - 小包厢</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              预订日期 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.reservationDate"
              type="date"
              required
              :min="today"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="grid grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              开始时间 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.startTime"
              type="time"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              时长(小时) <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.duration"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">选择时长</option>
              <option :value="1">1小时</option>
              <option :value="2">2小时</option>
              <option :value="3">3小时</option>
              <option :value="4">4小时</option>
              <option :value="5">5小时</option>
              <option :value="6">6小时</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              预付定金
            </label>
            <input
              v-model.number="form.deposit"
              type="number"
              min="0"
              step="10"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="0"
            />
          </div>
        </div>

        <!-- 备注 -->
        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">
            备注信息
          </label>
          <textarea
            v-model="form.notes"
            rows="3"
            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入备注信息（可选）"
          ></textarea>
        </div>

        <!-- 预计费用 -->
        <div v-if="estimatedAmount > 0" class="bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center justify-between">
            <span class="text-sm text-slate-600">预计费用：</span>
            <span class="text-lg font-bold text-blue-600">¥{{ estimatedAmount }}</span>
          </div>
          <div v-if="form.deposit > 0" class="flex items-center justify-between mt-2">
            <span class="text-sm text-slate-600">预付定金：</span>
            <span class="text-sm text-slate-600">¥{{ form.deposit }}</span>
          </div>
          <div v-if="form.deposit > 0" class="flex items-center justify-between mt-1">
            <span class="text-sm text-slate-600">到店支付：</span>
            <span class="text-sm font-medium text-slate-800">¥{{ estimatedAmount - form.deposit }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-slate-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!isFormValid"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            确认预订
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { ReservationForm } from '../../types/reservation';

defineOptions({
  name: 'ReservationForm'
});

// Emits
const emit = defineEmits<{
  close: [];
  submit: [data: ReservationForm];
}>();

// 表单数据
const form = ref<ReservationForm>({
  customerName: '',
  customerPhone: '',
  roomId: '',
  reservationDate: '',
  startTime: '',
  duration: 0,
  notes: '',
  deposit: 0
});

// 今天的日期
const today = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 房间价格配置
const roomPrices: Record<string, number> = {
  'V01': 200, 'V02': 200, 'V03': 200, 'V04': 200, 'V05': 200,
  'M01': 150, 'M02': 150, 'M03': 150, 'M04': 150, 'M05': 150,
  'S01': 100, 'S02': 100, 'S03': 100, 'S04': 100, 'S05': 100
};

// 预计费用
const estimatedAmount = computed(() => {
  if (!form.value.roomId || !form.value.duration) return 0;
  const hourlyRate = roomPrices[form.value.roomId] || 0;
  return hourlyRate * form.value.duration;
});

// 表单验证
const isFormValid = computed(() => {
  return form.value.customerName &&
         form.value.customerPhone &&
         form.value.roomId &&
         form.value.reservationDate &&
         form.value.startTime &&
         form.value.duration > 0;
});

// 提交表单
const handleSubmit = () => {
  if (isFormValid.value) {
    emit('submit', form.value);
  }
};
</script>
