import { defineStore } from "pinia";
import { ref, reactive, computed } from "vue";
import type { Room } from "@/views/cashier/types/room";
import { ElMessage } from "element-plus";

export interface Transaction {
  id: string;
  roomId: string;
  type: "checkin" | "checkout" | "extend" | "consume";
  amount: number;
  timestamp: Date;
  description: string;
  operator: string;
}

export interface RoomOperation {
  key: string;
  label: string;
  icon: string;
  type: string;
  action: string;
}

/**
 * 收银系统 - 交易管理 Store
 */
export const useCashierTransactionStore = defineStore("cashier-transaction", () => {
  // ==================== 状态 ====================
  const transactions = reactive<Transaction[]>([]);
  const currentTransaction = ref<Transaction | null>(null);
  const isProcessing = ref(false);

  // ==================== 计算属性 ====================
  
  // 今日交易统计
  const todayStats = computed(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayTransactions = transactions.filter(t => 
      t.timestamp >= today
    );
    
    const totalAmount = todayTransactions.reduce((sum, t) => sum + t.amount, 0);
    const checkinCount = todayTransactions.filter(t => t.type === "checkin").length;
    const checkoutCount = todayTransactions.filter(t => t.type === "checkout").length;
    
    return {
      totalAmount,
      transactionCount: todayTransactions.length,
      checkinCount,
      checkoutCount,
      averageAmount: todayTransactions.length > 0 ? totalAmount / todayTransactions.length : 0
    };
  });

  // 最近交易记录
  const recentTransactions = computed(() => {
    return transactions
      .slice()
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);
  });

  // ==================== 方法 ====================

  // 获取房间可用操作
  const getRoomActions = (status: Room["status"]): RoomOperation[] => {
    const actions: Record<Room["status"], RoomOperation[]> = {
      free: [
        { key: "checkin", label: "开房", icon: "door-open", type: "primary", action: "checkin" },
        { key: "book", label: "预订", icon: "calendar", type: "success", action: "book" }
      ],
      occupied: [
        { key: "checkout", label: "结账", icon: "cash-register", type: "danger", action: "checkout" },
        { key: "extend", label: "续时", icon: "clock", type: "warning", action: "extend" },
        { key: "consume", label: "消费", icon: "shopping-cart", type: "primary", action: "consume" }
      ],
      cleaning: [
        { key: "clean_done", label: "清洁完成", icon: "check", type: "success", action: "clean_done" }
      ],
      booked: [
        { key: "checkin", label: "入住", icon: "door-open", type: "primary", action: "checkin" },
        { key: "cancel", label: "取消预订", icon: "times", type: "danger", action: "cancel" }
      ],
      maintenance: [
        { key: "repair_done", label: "维修完成", icon: "tools", type: "success", action: "repair_done" }
      ],
      disabled: []
    };
    
    return actions[status] || [];
  };

  // 处理房间操作
  const handleRoomAction = async (action: string, room: Room): Promise<boolean> => {
    if (isProcessing.value) {
      ElMessage.warning("正在处理中，请稍候...");
      return false;
    }

    isProcessing.value = true;
    
    try {
      switch (action) {
        case "checkin":
          return await handleCheckin(room);
        case "checkout":
          return await handleCheckout(room);
        case "extend":
          return await handleExtend(room);
        case "consume":
          return await handleConsume(room);
        case "clean_done":
          return await handleCleanDone(room);
        case "book":
          return await handleBook(room);
        case "cancel":
          return await handleCancel(room);
        case "repair_done":
          return await handleRepairDone(room);
        default:
          ElMessage.warning(`未知操作: ${action}`);
          return false;
      }
    } catch (error) {
      console.error("操作失败:", error);
      ElMessage.error("操作失败，请重试");
      return false;
    } finally {
      isProcessing.value = false;
    }
  };

  // 开房操作
  const handleCheckin = async (room: Room): Promise<boolean> => {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const transaction: Transaction = {
      id: `T${Date.now()}`,
      roomId: room.id,
      type: "checkin",
      amount: 200, // 基础房费
      timestamp: new Date(),
      description: `房间 ${room.id} 开房`,
      operator: "收银员"
    };
    
    transactions.push(transaction);
    ElMessage.success(`房间 ${room.id} 开房成功`);
    return true;
  };

  // 结账操作
  const handleCheckout = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const transaction: Transaction = {
      id: `T${Date.now()}`,
      roomId: room.id,
      type: "checkout",
      amount: room.total || 0,
      timestamp: new Date(),
      description: `房间 ${room.id} 结账`,
      operator: "收银员"
    };
    
    transactions.push(transaction);
    ElMessage.success(`房间 ${room.id} 结账成功，金额: ¥${room.total}`);
    return true;
  };

  // 续时操作
  const handleExtend = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const transaction: Transaction = {
      id: `T${Date.now()}`,
      roomId: room.id,
      type: "extend",
      amount: 100, // 续时费用
      timestamp: new Date(),
      description: `房间 ${room.id} 续时 2小时`,
      operator: "收银员"
    };
    
    transactions.push(transaction);
    ElMessage.success(`房间 ${room.id} 续时成功`);
    return true;
  };

  // 消费操作
  const handleConsume = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.info("消费功能开发中...");
    return false;
  };

  // 清洁完成
  const handleCleanDone = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success(`房间 ${room.id} 清洁完成`);
    return true;
  };

  // 预订操作
  const handleBook = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.info("预订功能开发中...");
    return false;
  };

  // 取消预订
  const handleCancel = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success(`房间 ${room.id} 预订已取消`);
    return true;
  };

  // 维修完成
  const handleRepairDone = async (room: Room): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success(`房间 ${room.id} 维修完成`);
    return true;
  };

  // 添加交易记录
  const addTransaction = (transaction: Omit<Transaction, "id" | "timestamp">) => {
    const newTransaction: Transaction = {
      ...transaction,
      id: `T${Date.now()}`,
      timestamp: new Date()
    };
    
    transactions.push(newTransaction);
    return newTransaction;
  };

  // 获取房间交易历史
  const getRoomTransactions = (roomId: string): Transaction[] => {
    return transactions.filter(t => t.roomId === roomId);
  };

  // 清空交易记录
  const clearTransactions = () => {
    transactions.splice(0, transactions.length);
  };

  return {
    // 状态
    transactions,
    currentTransaction,
    isProcessing,
    
    // 计算属性
    todayStats,
    recentTransactions,
    
    // 方法
    getRoomActions,
    handleRoomAction,
    addTransaction,
    getRoomTransactions,
    clearTransactions
  };
});

export const useCashierTransaction = () => useCashierTransactionStore();
