import DeviceManageList from "@/views/materialManagement/deviceManageList.vue";
export default {
  path: "/materialManagement",
  redirect:'/materialManagement/deviceManageList',
  meta: {
    title: "通道素材管理",
    rank: 890,
  // showLink:false,
  },
  children: [
    {
      path: "/materialManagement/deviceManageList",
      name: "deviceManage",
      component:DeviceManageList,
      meta: {
        title: "设备管理",
        showParent: true,
      },

    },
        {
      path: "/materialManagement/fileManage",
      name: "fileManage",
      component: () => import("@/views/materialManagement/fileManage.vue"),
      meta: {
        title: "素材管理",
        showParent: true,
      },

    },
      {
      path: "/materialManagement/programManageList",
      name: "programManageList",
      component: () => import("@/views/materialManagement/programManageList.vue"),
      meta: {
        title: "节目管理列表",
        showParent: true,
      },

    }
  ]
}

