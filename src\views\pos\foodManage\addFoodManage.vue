<template>
  <!-- 新建草稿箱 -->
  <div>
    <el-dialog
      v-model="addFoodManageDialog"
      fullscreen
      draggable
      @close="addDialogClose"
    >
      <el-button v-if="source !== 'published'" @click="existArrival"
        >选择已有商品</el-button
      >
      <el-button v-if="source !== 'published'" @click="newArrival"
        >添加新商品</el-button
      >
      <el-input
        v-model="foodManageSearch"
        style="max-width: 300px; margin-left: 15px"
        placeholder="请输入套餐/商品名称进行搜索"
        class="input-with-select"
        clearable
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button :icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      <pure-table
        :columns="computedColumns"
        :data="filteredData"
        style="height: 75vh"
        v-loading="addTableLoad"
      >
        <!--  使用 #name 插槽（与 columns 中的 slot: name 匹配） -->
        <template #ComboName="{ row }">
          <!-- 此处改为 #name -->
          <div>
            <div v-if="row.Type == 1">
              <!-- 单品 -->
              <div>{{ row.FdCName }}</div>
            </div>
            <div v-else>
              <div>{{ row.ComboName }}</div>
            </div>
          </div>
        </template>
        <template #content="{ row }">
          <div>
            <div v-if="row.Type == 1">
              <!-- 单品 -->
              <div>{{ row.FdCName }}</div>
            </div>
            <div v-else>
              <!-- 套餐 -->
              <!-- <div>{{ row.ComboName }}</div> -->

              <!-- 套餐 -->
              <div v-if="row.ComboItems?.length > 0">
                <span v-for="(item, index) in row.ComboItems" :key="index">
                  {{ item.PGFdCName }} (¥{{ item.CommodityPrice || "--" }}) ×
                  {{ item.PGFdQty || 1 }}
                  <br v-if="index !== row.ComboItems.length - 1" />
                </span>
              </div>
              <div v-else-if="row.PackageItems?.length > 0">
                <span v-for="(item, index) in row.PackageItems" :key="index">
                  {{ item.PGFdCName }} (¥{{ item.CommodityPrice || "--" }}) ×
                  {{ item.PGFdQty || 1 }}
                  <br v-if="index !== row.PackageItems.length - 1" />
                </span>
              </div>
            </div>
          </div>
        </template>

        <!-- 价格模式插槽（修正重复代码） -->
        <template #priceMode="{ row }">
          <div v-html="formattedDate(String(row.PriceMode))"></div>
          <!-- <div>
              {{ row.PriceMode }}：原价{{ row.MarketPrice }}，实际销售价格{{
                row.SalePrice
              }}
            </div> -->
        </template>
        <template #MarketPrice="{ row }">
          <div v-html="formattedDate(String(row.MarketPrice))"></div>
          <!-- <div>
              {{ row.PriceMode }}：原价{{ row.MarketPrice }}，实际销售价格{{
                row.SalePrice
              }}
            </div> -->
        </template>
        <template #SalePrice="{ row }">
          <div v-html="formattedDate(String(row.SalePrice))"></div>
          <!-- <div>
              {{ row.PriceMode }}：原价{{ row.MarketPrice }}，实际销售价格{{
                row.SalePrice
              }}
            </div> -->
        </template>
        <template #FtNo="{ row }">
          <!-- 通过对象映射实现数值转标签 -->
          {{ getCategoryLabel(row.FtNo) }}
        </template>

        <!-- 操作栏插槽 -->
        <template #operation="{ row }" v-if="source !== 'published'">
          <el-button
            type="primary"
            size="small"
            link
            @click="changeFoodManage(row)"
            v-if="source !== 'published'"
            >修改</el-button
          >
          <el-button
            type="primary"
            size="small"
            link
            @click="deleteFoodManage(row)"
            v-if="source !== 'published'"
            >删除</el-button
          >
          <el-button
            type="primary"
            v-if="source !== 'published' && (row.ComboNo || row.FdNo)"
            size="small"
            link
            @click="historyAdjustment(row)"
            style="margin-left: 0px"
            >查看历史调价</el-button
          >
        </template>
      </pure-table>
      <!-- <el-pagination
        size="small"
        background
        layout="prev, pager, next"
        :total="pagination.Records"
        :page-size="20"
        v-model:current-page="pagination.current"
        @current-change="handleCurrentChange"
        class="mt-1"
      /> -->
      <template #footer>
        <div class="dialog-footer button-container">
          <el-button v-if="source !== 'published'" @click="saveDraft"
            >保存到草稿</el-button
          >
          <el-button v-if="source !== 'published'" disabled
            >生成发文内容</el-button
          >
          <el-button
            v-if="source !== 'published'"
            disabled
            @click="synchronousStore"
            >同步到门店</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 3个弹窗 -->
    <addNewArrival v-model:newArrivalDialog="newArrivalDialog" />
    <chooseExistArrival
      v-model:existArrivalDialog="existArrivalDialog"
      v-model:forceCommodityMode="forceCommodityMode"
      source="addFoodManage"
    />
    <AdjustMent
      v-model:rowId="rowId"
      v-model:historyAdjustDialog="historyAdjustDialog"
      :history-data="historyData"
    />
  </div>
  <!-- 调价名称弹窗 -->
  <el-dialog v-model="nameDialogVisible" title="请输入调价名称" width="30%">
    <el-input v-model="adjustmentName" placeholder="请输入调价名称" />
    <template #footer>
      <el-button @click="nameDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmName">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, toRefs, watch, computed } from "vue";
import { utils } from "./modules/utils";
import addNewArrival from "./newArrival.vue";
import chooseExistArrival from "./existArrival.vue";
import {
  AddOrUpdateCommDraftRecords,
  CommSynchronousStore,
  GetCommHistoricalPrice
} from "@/api/addFoodManage";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import AdjustMent from "./historyAdjustments.vue";
const nameDialogVisible = ref(false);
const adjustmentName = ref("");
const tempSaveCallback = ref(null); // 用于保存回调函数
const reqSync = ref({
  UserId: "USER001",
  AdjustmentIDs: "EB0EA72A-AF69-4978-B1B4-8C31DEA3F9BA"
});
const {
  formattedDate,
  addFoodManangeTable,
  form,
  addPackageTableData,
  isAdd,
  generateUniqueId,
  isView,
  filteredData,
  adjustTableData
} = utils();

const forceCommodityMode = ref(false);
// const filteredData = computed(() => {
//   if (!foodManageSearch.value.trim()) {
//     return isView.value || []; // 无搜索词时直接返回全部数据
//   }

//   const keyword = foodManageSearch.value.trim().toLowerCase();
//   return (isView.value || []).filter(row => {
//     // 同时搜索单品名称和套餐名称
//     const type = Number(row.Type);
//     if (type === 1) {
//       return row.FdCName?.toLowerCase().includes(keyword);
//     } else {
//       return row.ComboName?.toLowerCase().includes(keyword);
//     }
//   });
// });
// 将 filteredData 改为 ref
// const filteredData = ref([]);
// 搜索框按钮绑定数据
const foodManageSearch = ref("");
// 监听搜索词和原始数据的变化，手动更新 filteredData
const adjustID = ref("");
watch(
  [foodManageSearch, isView],
  () => {
    if (!isView.value) return; // 确保原始数据存在

    const keyword = foodManageSearch.value.trim().toLowerCase();
    if (!keyword) {
      // 无搜索词时显示全部数据
      filteredData.value = [...isView.value];
      console.log(filteredData.value);
      if (
        filteredData.value[0] &&
        filteredData.value[0].AdjustmentID?.toString() !==
          adjustID.value.toString()
      ) {
        adjustID.value = filteredData.value[0].AdjustmentID?.toString() || "";
      }
      // adjustID.value = filteredData.value[0].AdjustmentID?.toString() || "";
    } else {
      // 按单品名称或套餐名称过滤（不区分大小写）
      filteredData.value = isView.value.filter(row => {
        const type = Number(row.Type);
        const searchKey = type === 1 ? row.FdCName : row.ComboName;
        return searchKey?.toLowerCase().includes(keyword);
      });
    }
  },
  { immediate: true } // 初始化时执行
);
const props = defineProps({
  addFoodManageDialog: Boolean,
  source: {
    type: String,
    default: "" // 设置默认值，避免 undefined
  }
});
// 解构
const { addFoodManageDialog } = toRefs(props);

// 传递给父组件
const emit = defineEmits(["update:addFoodManageDialog", "refreshParentData"]);
// 弹窗关闭事件
const addDialogClose = () => {
  addFoodManageDialog.value = false;
  emit("update:addFoodManageDialog", false);
};
// 添加新商品弹窗
const newArrivalDialog = ref(false);
// 添加新商品弹窗打开事件
const newArrival = () => {
  // 告诉子组件，现在是添加状态
  isAdd.value = true;
  newArrivalDialog.value = true;
};
// 选择已有商品弹窗
const existArrivalDialog = ref(false);
// 选择已有商品
const existArrival = () => {
  existArrivalDialog.value = true;
  forceCommodityMode.value = false;
};

watch(
  () => addFoodManangeTable.value,
  newVal => {
    console.log(newVal);
  }
);
// 修改表格中的数据
const changeFoodManage = row => {
  console.log(row);
  console.log("点击修改时的row.id:", row.DetailID); // 确认id值
  // 告诉子组件，现在是修改状态
  isAdd.value = false;

  newArrivalDialog.value = true;

  if (row.Type == "2") {
    // 处理套餐数据
    addPackageTableData.value = row.PackageItems;
    form.value = {
      delivery: true,
      ...row,
      id: row.id,
      FdCName: row.ComboName,
      ApplicableStores: row.ApplicableStores
        ? row.ApplicableStores.split(",")
        : null
    };
  } else if (row.Type == "1") {
    form.value = {
      delivery: false,
      ...row,
      id: row.id,
      FdCName: row.FdCName,
      ApplicableStores: row.ApplicableStores
        ? row.ApplicableStores.split(",")
        : null
    };
  }
};

// 删除表格中的数据
const deleteFoodManage = row => {
  const targetTable = filteredData.value;
  const index = targetTable.findIndex(item => item.id === row.id); // 使用唯一标识查找索引
  if (index !== -1) {
    targetTable.splice(index, 1);
  } else {
    ElMessage.warning("未找到对应数据");
  }
};

// 保存到草稿按钮事件
const saveDraft = () => {
  nameDialogVisible.value = true;
  adjustmentName.value = "";

  tempSaveCallback.value = async () => {
    if (!adjustmentName.value) {
      ElMessage.warning("请输入调价名称");
      return;
    }

    try {
      // 获取实际表格数据（解除Proxy响应式）
      const tableData = JSON.parse(JSON.stringify(filteredData.value));
      console.log("当前表格数据:", tableData);

      if (!tableData || tableData.length === 0) {
        ElMessage.warning("没有可保存的数据");
        return;
      }

      // 构建符合后端要求的请求数据结构
      const payload = {
        UserId: "USER001", // TODO: 替换为实际用户ID
        Name: adjustmentName.value,
        AdjustmentID: adjustID.value || null, // 更新时使用已有ID
        AddDraftRecordList: tableData.map(item => {
          // 公共字段
          const record = {
            Type: item.Type,
            FdQty: item.FdQty ?? 1, // 默认数量1
            FtNo: item.FtNo || "",
            MarketPrice: Number(item.MarketPrice) || 0,
            SalePrice: Number(item.SalePrice) || 0,
            PriceMode: item.PriceMode || "Fixed",
            ApplicableStores: item.ApplicableStores || "",
            DetailID: item.DetailID || null
          };

          // 区分单品和套餐
          if (item.Type === "1") {
            // 单品
            record.FdCName = item.FdCName || "";
            record.FdNo = item.FdNo || "";
            record.ComboName = null;
            record.ComboNo = null;
            record.PackageItems = null;
          } else if (item.Type === "2") {
            // 套餐
            record.FdCName = null;
            record.FdNo = null;
            record.ComboName = item.ComboName || ""; // 必须要有值
            record.ComboNo = item.ComboNo || ""; // 必须要有值
            record.PackageItems = (item.PackageItems || []).map(pkg => ({
              PGFdNo: pkg.PGFdNo || "", // 套餐商品编号
              PGFdQty: pkg.PGFdQty || 1, // 默认数量1
              CommodityPrice: String(pkg.CommodityPrice || "0") // 注意：可能需要字符串格式
            }));
          }

          return record;
        })
      };

      console.log("最终请求数据:", JSON.stringify([payload], null, 2));

      // 发送请求（确保AddOrUpdateCommDraftRecords正确配置）
      const res = await AddOrUpdateCommDraftRecords([payload]);

      ElMessage.success("保存成功");
      nameDialogVisible.value = false;
      
    } catch (error) {
      console.error("完整错误对象:", error);
      const errorMsg =
        error.response?.data?.message ||
        error.response?.data?.errors?.join(", ") ||
        error.message;
      ElMessage.error(`保存失败: ${errorMsg}`);
    }
  };
};
// 确认名称的回调
const confirmName = () => {
  if (tempSaveCallback.value) {
    tempSaveCallback.value();
  }
};
//同步门店
const synchronousStore = async () => {
  const res = await CommSynchronousStore(reqSync.value);
  console.log(res);
};
// 分页数据绑定
const pagination = ref({
  Records: 0,
  Total: 0,
  current: 1
});

// 搜索按钮点击事件
const handleSearch = () => {
  reqBody.value.Keyword = foodManageSearch.value.trim();
  pagination.value.current = 1;
  draftData();
};
// 表格数据请求参数
const reqBody = ref({
  "Paging.Rows": 200,
  "Paging.Page": pagination.value.current,
  "Paging.Sidx": "1",
  "Paging.Sord": "1",
  "Paging.Records": 1,
  UserId: "USER001",
  Keyword: ""
});
// 表格数据加载状态
const addTableLoad = ref(false);
// watch(
//   () => addFoodManageDialog.value,
//   newVal => {
//     if (newVal == true) {
//       // 修改加载状态
//       addTableLoad.value = true;
//       draftData();
//     }
//   }
// );

// 定义表格表头
const columns = [
  {
    label: "套餐/商品名称",
    prop: "ComboName",
    width: "140",
    slot: "ComboName", // 使用自定义插槽显示不同类型的名称
    fixed: true
  },
  {
    label: "套餐/商品明细",
    prop: "content",
    width: "200",
    slot: "content"
  },
  {
    label: "商品类别",
    prop: "FtNo",
    slot: "FtNo"
  },

  {
    label: "适用范围",
    prop: "priceMode",
    slot: "priceMode"
  },
  {
    label: "原价",
    prop: "MarketPrice",
    slot: "MarketPrice"
  },
  {
    label: "实际售价",
    prop: "SalePrice",
    slot: "SalePrice"
  },
  {
    label: "适用门店",
    prop: "content",
    width: "180",
    formatter: row => {
      if (!row.ApplicableStores) return ""; // 处理空值
      // 1. 拆分字符串为数组
      const storeNumbers = row.ApplicableStores.split(",");
      // 2. 查找对应的店铺对象
      const stores = storeNumbers.map(number => {
        return storeOptions.value.find(shop => shop.value === number) || "";
      });
      // 3. 提取店铺名称并拼接为字符串
      const storeNames = stores.map(store => store.label).join(",");
      return storeNames;
    }
  }
];
// 计算属性：动态生成表头
const computedColumns = computed(() => {
  // 已发布场景（source为'published'）不包含操作列
  if (props.source === "published") {
    return [...columns];
  }
  // 其他场景（草稿箱编辑）包含操作列
  return [
    ...columns,
    {
      label: "操作",
      width: "150",
      fixed: "right",
      slot: "operation"
    }
  ];
});

// 获取草稿箱表格数据
// const draftData = async () => {
//   const res = await GetCommDraftListRecord(reqBody.value);
//   console.log(res);
//   addFoodManangeTable.value = res.data.list.map(item => {
//     return {
//       ...item,
//       id: generateUniqueId() // 生成唯一id
//     };
//   });
//   addTableLoad.value = false;
//   pagination.value.Records = res.data.paging.Records;
//   console.log(addFoodManangeTable.value);
//   console.log(pagination.value);
// };
const storeOptions = ref([
  { label: "天河店", value: "1" },
  { label: "缤缤店", value: "2" },
  { label: "海印店", value: "3" },
  { label: "白云店", value: "4" },
  { label: "区庄店", value: "5" },
  { label: "番禺店", value: "6" },
  { label: "罗湖店", value: "7" },
  { label: "英德店", value: "8" },
  { label: "名堂", value: "9" }
]);
// 处理商品类别方法
const getCategoryLabel = ftNo => {
  const categoryMap = {
    1: "饮品",
    2: "甜点",
    3: "小吃"
  };
  return categoryMap[ftNo] || "未知类别"; // 处理异常值
};

const rowId = ref({
  ComboNo: "",
  FdNo: ""
});
// 历史调价弹窗
const historyData = ref([]);
const historyAdjustDialog = ref(false);
const historyAdjustment = async row => {
  console.log("查看历史调价:", row);
  // 构建请求参数
  const reqbody = {
    ...reqBody.value,
    ComboNo: row.ComboNo || "",
    FdNo: row.FdNo || ""
  };
  console.log(reqbody);
  // 显示加载状态（可选）
  // addTableLoad.value = true;

  // 调用接口获取历史价格
  const res = await GetCommHistoricalPrice(reqbody);
  console.log("历史价格响应:", res.data.list);

  historyData.value = res.data.list?.flatMap(item => item.Value || []) || [];

  console.log("处理后数据:", historyData.value); // 检查处理后的数据
  historyAdjustDialog.value = true;
};

// 分页处理
const handleCurrentChange = val => {
  pagination.value.current = val;
  reqBody.value["Paging.Page"] = val;
  draftData();
};
</script>

<style>
.button-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  gap: 8px; /* 按钮间距，可选 */
  padding: 16px; /* 内边距，可选 */
}
</style>
