<script setup>
import { onMounted, watch, ref, toRefs, reactive } from "vue";
import { ElMessage } from "element-plus";
import { Plus, WarningFilled } from "@element-plus/icons-vue";
import CouponDialog from "../active/chooseCoupons.vue";
import { coupon } from "./modules/utils";
import dayjs from "dayjs";
import {
  GetSalesAndCardsInfoRecord,
  EditorSalesAndCardsInfoRecord
} from "@/api/activeCoupon/activeCoupon";
const { couponValue, currentId } = coupon();
const tableRef = ref();
console.log(couponValue.value);
// 接收父组件的值
const props = defineProps({
  editVisible: Boolean,
  state: String
});
const { editVisible, state } = toRefs(props);
const emit = defineEmits(["update:editVisible"]);
// 关闭弹窗时触发的事件
const visibleClose = () => {
  resetData();
  emit("update:editVisible", false);
};
const title = ref("编辑活动");
// 定义数据初始状态
const initialState = {
  bodyList: {
    title: "堂会活动",
    isVip: false,
    style: {
      sceneColor: "#5d5c5b"
    },
    hideHomeButton: false,
    key: "LJ_6125_HY4M1",
    isShowTime: false,
    BodyEexplain: {
      Top: "微信搜索 “堂会” 公众号并关注，查看券使用详情"
    },
    actionEvent: {},
    trigger: {},
    reOnly: true,
    isShowItemCou: true,
    isShowTitleCou: false,
    isTest: false,
    state: 1,
    msg: "查看我的优惠券",
    GrouponKey: "",
    cardList: []
  },
  form: {
    value2: []
  }
};

// 数据清空
const resetData = () => {
  // 1. 重置活动基本信息
  bodyList.value = { ...initialState.bodyList };
  // 2. 重置表单数据
  form.value = { ...initialState.form };
};

// 监听父组件传来的值，如果为true则打开弹窗
watch(
  () => props.editVisible,
  async newVal => {
    if (newVal) {
      if (state.value == "edit" || state.value == "see") {
        saleDialogLoad.value = true;
        title.value = "编辑活动";
        const reqBody = {
          TypeName: "CardSheet",
          "Paging.Rows": "10",
          "Paging.Page": "1",
          "Paging.Sord": "1",
          "Paging.Sidx": "1",
          "Paging.Records": "1",
          CardSheetId: currentId.value
        };
        console.log(reqBody);
        const res = await GetSalesAndCardsInfoRecord(reqBody);
        bodyList.value = res.data.list[0];
        form.value.value2 = [bodyList.value.begintime, bodyList.value.endtime];
        console.log(bodyList.value.cardList);
        console.log(res);
        title.value = "编辑活动";
        saleDialogLoad.value = false;
      } else if (state.value == "add") {
        title.value = "新增活动";
        resetData();
        form.value.value2 = [bodyList.value.begintime, bodyList.value.endtime];
      }
    }
  }
);
watch(
  () => couponValue.value,
  newVal => {
    if (newVal) {
      console.log(newVal);
      bodyList.value.cardList = newVal;
    }
  }
);
const saleDialogLoad = ref(false);
// 监听弹窗传递的state
watch(
  () => props.state,
  async newVal => {}
);

const bodyList = ref({
  ...initialState.bodyList
});
const Groupon = ref({
  Name: "堂会活动",
  sale: 10,
  marketSale: 20,
  list: "卡券详情"
});
const form = ref({
  value2: [bodyList.value.begintime, bodyList.value.endtime]
});

// 生成带前缀的日期字符串
const generateDateString = () => {
  return dayjs().format("YYYYMMDDHHmmss"); // 格式如: 20250618
};

// 生成自增ID的函数
const createIdGenerator = prefix => {
  let counter = 1;
  return () => {
    const id = `${prefix}${counter.toString().padStart(3, "0")}`;
    counter++;
    return id;
  };
};
const formRef = ref(null);
// 表单验证规则
const rules = reactive({
  title: [
    { required: true, message: "请输入活动名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
  ]
});
const fromRules = async () => {
  try {
    await formRef.value.validate();
    if (form.value.value2[0] === "" || form.value.value2[1] === "") {
      ElMessage({
        message: "请选择时间",
        type: "warning"
      });
      return false;
    }
    if (bodyList.value.cardList.length === 0) {
      ElMessage({
        message: "请选择卡券",
        type: "warning"
      });
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
};
const onSubmit = async () => {
  const isValid = await fromRules();
  // 验证不通过则直接返回
  if (!isValid) return;
  console.log(bodyList.value);
  saleDialogLoad.value = true;
  const [startTime, endTime] = form.value.value2;
  const dateStr = generateDateString();
  const salePrefix = `SALEKBOSS${dateStr}`;

  // // 创建G_id生成器
  const generateGId = createIdGenerator(salePrefix);

  // 格式化为字符串（可选）
  const formattedData = {
    starttime: formatDate(startTime, "YYYY-MM-DD HH:mm:ss"),
    endtime: formatDate(endTime, "YYYY-MM-DD HH:mm:ss")
  };
  if (state.value == "add") {
    bodyList.value = {
      ...bodyList.value, // 保留原有属性
      t: `SALE${dateStr}`,
      begintime: formattedData.starttime,
      endtime: formattedData.endtime,
      CreationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      Type: "CardSheet",
      key: `KBOSS${dateStr}`,
      couTag: "",
      IsDelete: false,
      GrouponKey: bodyList.value.cardList[0].GrouponKey,
      cardList: bodyList.value.cardList.map(item => ({
        G_id: "", // 生成新的 G_id
        qty: 1, // 设置默认数量
        GrouponName: item.GrouponName,
        GrouponPrice: item.GrouponPrice,
        GoodsPrice: item.GoodsPrice,
        Explain: item.Explain ? item.Explain : "无",
        GrouponKey: item.GrouponKey ? item.GrouponKey : "09808989890"
        // 其他需要保留的字段...
      }))
    };
    const jsonString = JSON.stringify(bodyList.value);
    const req = {
      Data: jsonString
      // CardSheetJsonData: String(JSON.stringify(bodyList.value))
    };
    const res = await EditorSalesAndCardsInfoRecord(req);
    console.log(res);
    if (res.state === 200) {
      ElMessage({
        message: "保存成功",
        type: "success"
      });
      bodyList.value.CardSheetId = res.data[0].DataId;
      bodyList.value._id = res.data[0].IdList;
      saleDialogLoad.value = false;
    }
  } else {
    bodyList.value = {
      ...bodyList.value, // 保留原有属性
      t: `SALE${dateStr}`,
      begintime: formattedData.starttime,
      endtime: formattedData.endtime,
      key: `KBOSS${dateStr}`,
      couTag: "",
      CreationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      Type: "CardSheet",
      IsDelete: false,
      GrouponKey: bodyList.value.cardList[0].GrouponKey,
      cardList: bodyList.value.cardList.map(item => ({
        G_id: "",
        qty: 1, // 设置默认数量
        GrouponName: item.GrouponName,
        GrouponPrice: item.GrouponPrice,
        GoodsPrice: item.GoodsPrice,
        Explain: item.Explain ? item.Explain : "无",
        GrouponKey: item.GrouponKey
        // 其他需要保留的字段...
      }))
    };
    console.log("开始时间:", startTime);
    console.log("结束时间:", endTime);
    console.log("格式化后:", formattedData);
    console.log(bodyList.value);
    const jsonString = JSON.stringify(bodyList.value);
    const req = {
      Data: jsonString
    };
    const res = await EditorSalesAndCardsInfoRecord(req);
    console.log(res);
    if (res.state === 200) {
      ElMessage({
        message: "保存成功",
        type: "success"
      });
      saleDialogLoad.value = false;
    }
  }
};

// 日期格式化函数（可使用dayjs或原生）
const formatDate = (date, format) => {
  return dayjs(date).format(format);
};
// 卡券弹窗
const couponDialog = ref(false);
// 卡券弹窗状态
const couponState = ref("coupon");
//选择卡券信息
const chooseCoupons = () => {
  couponDialog.value = true;
};
</script>

<template>
  <div v-loading.fullscreen.lock="saleDialogLoad">
    <el-dialog
      v-model="editVisible"
      :title="title"
      top="0px"
      width="100%"
      style="height: 100%; margin: 0px"
      @close="visibleClose()"
    >
      <div class="container">
        <div class="con-left">
          <div class="left-body">
            <div class="left-top">
              <div class="left-top-tittle">
                {{ bodyList.title }}
              </div>
              <div class="left-top-name">
                <div class="left-top-body">
                  <p class="d-center d-icon">
                    <el-icon color="#f76260" size="50px"
                      ><WarningFilled
                    /></el-icon>
                  </p>
                  <p class="d-center d-tit">已领取</p>
                  <p class="d-center coupon">
                    {{ bodyList.cardList[0]?.GrouponName || "" }}
                  </p>
                  <p class="d-center d-time">
                    领券时间: {{ bodyList.begintime }} 至 {{ bodyList.endtime }}
                  </p>
                  <p class="d-center d-mes">
                    微信搜索"堂会"公众号并关注,查看券使用详情
                  </p>
                </div>
              </div>
              <div class="left-top-text">优惠卷详情</div>
            </div>
            <div
              class="left-bottom"
              v-html="bodyList.cardList[0]?.Explain || '详情信息'"
            ></div>
          </div>
        </div>
        <div class="con-right">
          <el-form
            :model="bodyList"
            label-width="auto"
            style="max-width: 80%; margin-left: 20px"
            :rules="rules"
            ref="formRef"
          >
            <el-form-item label="活动名称:" prop="title">
              <el-input
                maxlength="50"
                show-word-limit
                style="width: 400px"
                v-model="bodyList.title"
                :disabled="state === 'see'"
              />
            </el-form-item>
            <el-form-item label="可用时间:">
              <div style="width: 350px">
                <el-date-picker
                  v-model="form.value2"
                  type="datetimerange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  date-format="YYYY/MM/DD ddd"
                  time-format="A hh:mm:ss"
                  :disabled="state === 'see'"
                  class="custom-date-picker"
                />
              </div>
            </el-form-item>
            <el-form-item label="选择卡券:">
              <p v-if="bodyList.cardList && bodyList.cardList.length > 0">
                {{ bodyList.cardList[0].GrouponName || "" }}
              </p>
              <el-button
                v-if="state !== 'see'"
                type="primary"
                @click="chooseCoupons()"
                style="margin-left: 10px"
                >选择卡券信息
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="state !== 'see'" type="primary" @click="onSubmit"
                >保存</el-button
              >
              <el-button @click="visibleClose()">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <CouponDialog
        v-model:couponDialog="couponDialog"
        v-model:couponState="couponState"
      />
    </el-dialog>
  </div>
</template>

<style lang="scss">
.container {
  width: 100%;
  height: 100% !important;
  display: flex;
  margin: 0 auto;
}

.el-dialog__body {
  height: 90% !important;
}
</style>
<style scoped>
.con-left {
  width: 40%;
  max-height: 100%;
}

.con-right {
  flex: 1;
  height: 100%;
  width: 60%;
  background-color: #fff;
}

.left-body {
  width: 80%;
  margin: 0px auto;
  height: 100%;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
}

.left-top {
  width: 100%;
}

.left-top-tittle {
  background-color: #fff;
  text-align: center;
  font-size: 15px;
  font-weight: 700;
  padding: 5px 0px;
  color: black;
}

.left-top-img {
  width: 100%;
}

.left-top-text {
  background-color: #fff;
  font-size: large;
  color: black;
  padding-left: 5px;
  border-left: 4px solid #addaaf;
}

.left-bottom {
  height: 40%;
  overflow-y: auto;
  background-color: #fff;
  margin-top: 10px;
  color: black;
  padding: 10px;
}
.item-time {
  font-weight: 700;
}

.item-del {
  font-size: 12px;
  color: #a3a3a3;
}

.input-number {
  width: 100px;
}

.left-top-body {
  background-color: white;
  margin-bottom: 10px;
  margin-top: 2px;
}
.d-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.coupon {
  font-size: large;
  font-weight: 600;
  color: white;
  padding: 30px;
  margin: 20px 40px;
  background-color: #5d95df;
}
.d-icon {
  padding-top: 20px;
}
.d-tit {
  font-size: 18px;
  color: black;
  margin-top: 20px;
}
.d-time {
  color: black;
  font-size: 16px;
  margin-bottom: 10px;
}
.d-mes {
  padding-bottom: 20px;
}
</style>
