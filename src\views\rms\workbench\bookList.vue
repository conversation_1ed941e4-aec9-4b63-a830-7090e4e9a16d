<template>
  <div @click="closeMenu" @contextmenu.prevent="closeMenu">
    <!-- 数据表格 -->
    <el-table
      :data="bookData"
      style="width: 100%;height: 400px;"
      :row-class-name="tableRowClassName"
      @row-contextmenu="handleRightClick"
      class="custom-table"
      border
    >
      <el-table-column prop="date" label="预约号"  sortable   />
      <el-table-column prop="name" label="姓名"  sortable  />
      <!-- <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="number" label="人数" />
      <el-table-column prop="address1" label="订金" />
      <el-table-column prop="address" label="已付" />
    </el-table>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenu.visible"
      class="context-menu"
      :style="{
        left: contextMenu.left + 'px',
        top: contextMenu.top + 'px'
      }"
    >
      <div
        class="menu-item"
        v-for="item in menuItems"
        :key="item.key"
        @click="handleMenuClick(item.key)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
  bookLoading: Boolean,
  bookData: Array
});





// 右键菜单状态
const contextMenu = ref({
  visible: false,
  left: 0,
  top: 0,
  currentRow: null
})

// 菜单项配置
const menuItems = ref([
  { key: 'edit', label: '编辑' },
  { key: 'delete', label: '删除' },
  { key: 'view', label: '查看详情' }
])

// 根据状态返回行样式类名
const tableRowClassName = ({ row }) => {
  if (row.status === '1') return 'success-row'
  if (row.status === '2') return 'warning-row'
  if (row.status === '3') return 'danger-row'
  return ''
}

// 获取状态标签类型
const getStatusType = (status) => {
  const map = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return map[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const map = {
    '1': '正常',
    '2': '警告',
    '3': '危险'
  }
  return map[status] || '未知'
}

// 处理右键点击
const handleRightClick = (row, column, event) => {
  contextMenu.value = {
    visible: true,
    left: event.clientX,
    top: event.clientY,
    currentRow: row
  }
  event.preventDefault()
}

// 关闭菜单
const closeMenu = () => {
  contextMenu.value.visible = false
}

// 处理菜单项点击
const handleMenuClick = (key) => {
  const row = contextMenu.value.currentRow
  switch (key) {
    case 'edit':
      alert(`编辑 ${row.name}`)
      break
    case 'delete':
      if (confirm(`确定删除 ${row.name} 吗？`)) {
        tableData.value = tableData.value.filter(item => item !== row)
      }
      break
    case 'view':
      alert(`查看 ${row.name} 的详情`)
      break
  }
  closeMenu()
}
</script>

<style>
/* 行状态颜色 */
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .danger-row {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}
.custom-table .el-table__row {
  height: 5px; /* 设置行高 */
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f5f7fa;
}
</style>