<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="users" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">顾客管理</h2>
            <p class="text-sm text-slate-500">管理顾客信息和消费记录</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 工具栏 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-100">
        <div class="flex items-center space-x-4">
          <!-- 等级筛选 -->
          <select
            v-model="currentLevel"
            class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="">全部等级</option>
            <option value="bronze">青铜会员</option>
            <option value="silver">白银会员</option>
            <option value="gold">黄金会员</option>
            <option value="platinum">铂金会员</option>
            <option value="diamond">钻石会员</option>
          </select>

          <!-- 搜索框 -->
          <div class="relative">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索姓名或手机号..."
              class="pl-10 pr-4 py-2 w-64 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <font-awesome-icon
              icon="search"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"
            />
          </div>
        </div>

        <button
          @click="showAddCustomer = true"
          class="flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
        >
          <font-awesome-icon icon="user-plus" class="mr-2" />
          新增顾客
        </button>
      </div>

      <!-- 顾客列表 -->
      <div class="flex-1 overflow-auto p-6">
        <div class="grid gap-4">
          <div
            v-for="customer in filteredCustomers"
            :key="customer.id"
            class="bg-white border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <font-awesome-icon icon="user" class="text-purple-600" />
                </div>
                <div>
                  <div class="flex items-center space-x-2">
                    <h3 class="font-semibold text-slate-800">{{ customer.name }}</h3>
                    <span
                      :class="[
                        'px-2 py-1 rounded-full text-xs font-medium',
                        getLevelClass(customer.level)
                      ]"
                    >
                      {{ getLevelText(customer.level) }}
                    </span>
                  </div>
                  <p class="text-sm text-slate-500">{{ customer.phone }}</p>
                  <div class="flex items-center space-x-4 mt-1">
                    <span class="text-xs text-slate-600">积分: {{ customer.points }}</span>
                    <span class="text-xs text-slate-600">消费: ¥{{ customer.totalConsumption }}</span>
                    <span class="text-xs text-slate-600">到店: {{ customer.visitCount }}次</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <span class="text-sm text-slate-500">
                  最后到店: {{ customer.lastVisitDate || '未到店' }}
                </span>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-1">
                  <button
                    @click="viewCustomerDetail(customer)"
                    class="w-8 h-8 flex items-center justify-center text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                    title="查看详情"
                  >
                    <font-awesome-icon icon="eye" />
                  </button>
                  <button
                    @click="editCustomer(customer)"
                    class="w-8 h-8 flex items-center justify-center text-green-500 hover:bg-green-50 rounded-lg transition-colors"
                    title="编辑信息"
                  >
                    <font-awesome-icon icon="edit" />
                  </button>
                  <button
                    @click="viewConsumptionHistory(customer)"
                    class="w-8 h-8 flex items-center justify-center text-purple-500 hover:bg-purple-50 rounded-lg transition-colors"
                    title="消费记录"
                  >
                    <font-awesome-icon icon="history" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex items-center justify-between p-6 border-t border-slate-200">
        <span class="text-sm text-slate-500">
          共 {{ totalCustomers }} 位顾客
        </span>
        <div class="flex space-x-2">
          <button
            :disabled="currentPage === 1"
            @click="currentPage--"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            上一页
          </button>
          <span class="px-3 py-1">{{ currentPage }} / {{ totalPages }}</span>
          <button
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 新增顾客弹窗 -->
    <customer-form
      v-if="showAddCustomer"
      @close="showAddCustomer = false"
      @submit="handleAddCustomer"
    />

    <!-- 顾客详情弹窗 -->
    <customer-detail
      v-if="showCustomerDetail"
      :customer="selectedCustomer"
      @close="showCustomerDetail = false"
    />

    <!-- 消费记录弹窗 -->
    <consumption-history
      v-if="showConsumptionHistory"
      :customer="selectedCustomer"
      @close="showConsumptionHistory = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Customer, CustomerLevel } from '../../types/customer';
import CustomerForm from './customer-form.vue';
import CustomerDetail from './customer-detail.vue';
import ConsumptionHistory from './consumption-history.vue';

defineOptions({
  name: 'CustomerManagement'
});

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const customers = ref<Customer[]>([]);
const currentLevel = ref<CustomerLevel | ''>('');
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const showAddCustomer = ref(false);
const showCustomerDetail = ref(false);
const showConsumptionHistory = ref(false);
const selectedCustomer = ref<Customer | null>(null);

// 模拟数据
const mockCustomers: Customer[] = [
  {
    id: '1',
    name: '张三',
    phone: '13800138001',
    email: '<EMAIL>',
    gender: 'male',
    birthday: '1990-05-15',
    level: 'gold',
    points: 1580,
    totalConsumption: 15800,
    visitCount: 25,
    lastVisitDate: '2025-01-15',
    membershipDate: '2023-06-10',
    notes: '常客，喜欢VIP包厢',
    isActive: true,
    createdAt: '2023-06-10 14:30:00',
    updatedAt: '2025-01-15 20:30:00'
  },
  {
    id: '2',
    name: '李四',
    phone: '13800138002',
    level: 'silver',
    points: 680,
    totalConsumption: 6800,
    visitCount: 12,
    lastVisitDate: '2025-01-10',
    membershipDate: '2024-03-20',
    isActive: true,
    createdAt: '2024-03-20 16:45:00',
    updatedAt: '2025-01-10 19:20:00'
  }
];

// 计算属性
const filteredCustomers = computed(() => {
  let filtered = customers.value;

  if (currentLevel.value) {
    filtered = filtered.filter(c => c.level === currentLevel.value);
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(c => 
      c.name.toLowerCase().includes(keyword) ||
      c.phone.includes(keyword)
    );
  }

  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

const totalCustomers = computed(() => customers.value.length);
const totalPages = computed(() => Math.ceil(totalCustomers.value / pageSize.value));

// 方法
const getLevelClass = (level: CustomerLevel): string => {
  const classes = {
    bronze: 'bg-orange-100 text-orange-800',
    silver: 'bg-gray-100 text-gray-800',
    gold: 'bg-yellow-100 text-yellow-800',
    platinum: 'bg-blue-100 text-blue-800',
    diamond: 'bg-purple-100 text-purple-800'
  };
  return classes[level];
};

const getLevelText = (level: CustomerLevel): string => {
  const texts = {
    bronze: '青铜',
    silver: '白银',
    gold: '黄金',
    platinum: '铂金',
    diamond: '钻石'
  };
  return texts[level];
};

const viewCustomerDetail = (customer: Customer) => {
  selectedCustomer.value = customer;
  showCustomerDetail.value = true;
};

const editCustomer = (customer: Customer) => {
  console.log('编辑顾客:', customer);
};

const viewConsumptionHistory = (customer: Customer) => {
  selectedCustomer.value = customer;
  showConsumptionHistory.value = true;
};

const handleAddCustomer = (data: any) => {
  console.log('新增顾客:', data);
  showAddCustomer.value = false;
};

// 生命周期
onMounted(() => {
  customers.value = mockCustomers;
});
</script>
