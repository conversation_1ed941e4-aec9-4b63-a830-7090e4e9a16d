<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="calendar-alt" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">预订管理</h2>
            <p class="text-sm text-slate-500">管理房间预订信息</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 工具栏 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-100">
        <div class="flex items-center space-x-4">
          <!-- 状态筛选 -->
          <select
            v-model="currentStatus"
            class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部状态</option>
            <option value="pending">待确认</option>
            <option value="confirmed">已确认</option>
            <option value="cancelled">已取消</option>
            <option value="completed">已完成</option>
            <option value="no_show">未到店</option>
          </select>

          <!-- 日期筛选 -->
          <input
            v-model="dateFilter"
            type="date"
            class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />

          <!-- 搜索框 -->
          <div class="relative">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索顾客姓名或手机号..."
              class="pl-10 pr-4 py-2 w-64 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <font-awesome-icon
              icon="search"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"
            />
          </div>
        </div>

        <button
          @click="showAddReservation = true"
          class="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          <font-awesome-icon icon="plus" class="mr-2" />
          新增预订
        </button>
      </div>

      <!-- 预订列表 -->
      <div class="flex-1 overflow-auto p-6">
        <div class="grid gap-4">
          <div
            v-for="reservation in filteredReservations"
            :key="reservation.id"
            class="bg-white border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span class="text-blue-600 font-bold">{{ reservation.roomId }}</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800">{{ reservation.customerName }}</h3>
                  <p class="text-sm text-slate-500">{{ reservation.customerPhone }}</p>
                  <p class="text-sm text-slate-600">
                    {{ reservation.reservationDate }} {{ reservation.startTime }} - {{ reservation.endTime }}
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium',
                    getStatusClass(reservation.status)
                  ]"
                >
                  {{ getStatusText(reservation.status) }}
                </span>
                <span class="text-lg font-bold text-slate-800">¥{{ reservation.totalAmount }}</span>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-1">
                  <button
                    @click="editReservation(reservation)"
                    class="w-8 h-8 flex items-center justify-center text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    <font-awesome-icon icon="edit" />
                  </button>
                  <button
                    v-if="reservation.status === 'pending'"
                    @click="confirmReservation(reservation)"
                    class="w-8 h-8 flex items-center justify-center text-green-500 hover:bg-green-50 rounded-lg transition-colors"
                  >
                    <font-awesome-icon icon="check" />
                  </button>
                  <button
                    @click="cancelReservation(reservation)"
                    class="w-8 h-8 flex items-center justify-center text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <font-awesome-icon icon="times" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex items-center justify-between p-6 border-t border-slate-200">
        <span class="text-sm text-slate-500">
          共 {{ totalReservations }} 条预订记录
        </span>
        <div class="flex space-x-2">
          <button
            :disabled="currentPage === 1"
            @click="currentPage--"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            上一页
          </button>
          <span class="px-3 py-1">{{ currentPage }} / {{ totalPages }}</span>
          <button
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 新增预订弹窗 -->
    <reservation-form
      v-if="showAddReservation"
      @close="showAddReservation = false"
      @submit="handleAddReservation"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Reservation, ReservationStatus } from '../../types/reservation';
import ReservationForm from './reservation-form.vue';

defineOptions({
  name: 'ReservationManagement'
});

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const reservations = ref<Reservation[]>([]);
const currentStatus = ref<ReservationStatus | ''>('');
const dateFilter = ref('');
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const showAddReservation = ref(false);

// 模拟数据
const mockReservations: Reservation[] = [
  {
    id: '1',
    customerName: '张三',
    customerPhone: '13800138001',
    roomId: 'V01',
    roomType: 'VIP包厢',
    reservationDate: '2025-01-16',
    startTime: '19:00',
    endTime: '22:00',
    duration: 3,
    status: 'confirmed',
    totalAmount: 588,
    deposit: 100,
    notes: '生日聚会',
    createdAt: '2025-01-15 10:30:00',
    updatedAt: '2025-01-15 10:30:00',
    createdBy: 'cashier01'
  },
  {
    id: '2',
    customerName: '李四',
    customerPhone: '13800138002',
    roomId: 'M03',
    roomType: '中包厢',
    reservationDate: '2025-01-16',
    startTime: '20:00',
    endTime: '23:00',
    duration: 3,
    status: 'pending',
    totalAmount: 388,
    deposit: 50,
    createdAt: '2025-01-15 14:20:00',
    updatedAt: '2025-01-15 14:20:00',
    createdBy: 'cashier01'
  }
];

// 计算属性
const filteredReservations = computed(() => {
  let filtered = reservations.value;

  if (currentStatus.value) {
    filtered = filtered.filter(r => r.status === currentStatus.value);
  }

  if (dateFilter.value) {
    filtered = filtered.filter(r => r.reservationDate === dateFilter.value);
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(r => 
      r.customerName.toLowerCase().includes(keyword) ||
      r.customerPhone.includes(keyword)
    );
  }

  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

const totalReservations = computed(() => reservations.value.length);
const totalPages = computed(() => Math.ceil(totalReservations.value / pageSize.value));

// 方法
const getStatusClass = (status: ReservationStatus): string => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800',
    no_show: 'bg-gray-100 text-gray-800'
  };
  return classes[status];
};

const getStatusText = (status: ReservationStatus): string => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    cancelled: '已取消',
    completed: '已完成',
    no_show: '未到店'
  };
  return texts[status];
};

const editReservation = (reservation: Reservation) => {
  console.log('编辑预订:', reservation);
};

const confirmReservation = (reservation: Reservation) => {
  reservation.status = 'confirmed';
  console.log('确认预订:', reservation);
};

const cancelReservation = (reservation: Reservation) => {
  reservation.status = 'cancelled';
  console.log('取消预订:', reservation);
};

const handleAddReservation = (data: any) => {
  console.log('新增预订:', data);
  showAddReservation.value = false;
};

// 生命周期
onMounted(() => {
  reservations.value = mockReservations;
});
</script>
