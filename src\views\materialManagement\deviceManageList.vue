<template>
  <div>
    <div class="class-management">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索设备..."
          clearable
          class="search-input"
          @clear="fetchDevices"
          @change="fetchDevices"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="fetchDevices">
          <el-icon><Search /></el-icon>
          查询设备
        </el-button>
        <el-button type="success" @click="showDialog('add')">
          <el-icon><Plus /></el-icon>
          新增设备
        </el-button>
      </div>

      <!-- 设备卡片列表 -->
      <el-row
        v-if="modelList.length"
        :gutter="20"
        v-loading="loading"
        class="class-list"
      >
        <el-col
          v-for="item in modelList"
          :key="item.DeviceID"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <el-card class="class-card">
            <template #header>
              <div class="class-header">
                <el-tag
                  :type="item.Status ? 'success' : 'danger'"
                  effect="dark"
                >
                  {{ item.Status ? "在线" : "离线" }}
                </el-tag>
                <span class="title-name">{{ item.DeviceName }}</span>
              </div>
            </template>

            <div class="card-content">
              <div class="info-item">
                <el-icon><Key /></el-icon>
                <span class="label">设备绑定码：</span>
                <span class="value">{{ item.DeviceID }}</span>
              </div>

              <div class="info-item">
                <el-icon><Clock /></el-icon>
                <span class="label">注册时间：</span>
                <span class="value">{{ formatDate(item.CreatedTime) }}</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button plain @click="showModelDatils(item.DeviceID)"
                >投放节目</el-button
              >
              <el-tooltip content="编辑设备" placement="top">
                <el-button
                  type="primary"
                  plain
                  @click="showDialog('edit', item)"
                  >编辑</el-button
                >
              </el-tooltip>

              <el-popconfirm
                title="确认删除该设备?"
                @confirm="deleteDevice(item.DeviceID)"
              >
                <template #reference>
                  <el-button type="danger" plain>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-empty v-else description="未查询到记录" />
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.Rows"
        :total="pagination.Records"
        :page-sizes="[5, 10, 15]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handlePaginationChange"
        @current-change="handlePaginationChange"
      />
    </div>
    <!-- 设备表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增设备' : '编辑设备'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item
          v-if="formData.DeviceID != 0"
          label="设备码"
          prop="DeviceID"
        >
          <el-input disabled v-model="formData.DeviceID" />
        </el-form-item>
        <el-form-item label="设备名称" prop="DeviceName">
          <el-input v-model="formData.DeviceName" />
        </el-form-item>
        <el-form-item label="门店" prop="DeviceModel">
          <!--<el-input v-model="formData.DeviceName" />-->
        </el-form-item>
        <el-form-item label="设备型号" prop="DeviceModel">
          <el-input v-model="formData.DeviceModel" />
        </el-form-item>
        <!-- <el-form-item label="设备方向" prop="DeviceOrientation">
          <el-input v-model="formData.DeviceOrientation" />
        </el-form-item>
        <el-form-item label="设备高度" prop="DeviceHeight">
          <el-input v-model="formData.DeviceHeight" />
        </el-form-item>
        <el-form-item label="设备宽度" prop="DeviceWidth">
          <el-input v-model="formData.DeviceWidth" />
        </el-form-item>
        <el-form-item label="设备分辨率" prop="DeviceResolution">
          <el-input v-model="formData.DeviceResolution" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="在线"
            inactive-text="离线"
          />
        </el-form-item>-->
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>
    <!-- 投放任务对话框 -->
    <el-dialog v-model="dialogAdCampaignVisible" width="700px">
      <el-descriptions title="设备信息" :column="2" border>
        <el-descriptions-item label="编码">{{
          AdCampaignShowData.DeviceID
        }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{
          AdCampaignShowData.DeviceName
        }}</el-descriptions-item>
      </el-descriptions>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="投放任务列表" name="AdCampaignlist">
          <el-button type="primary" @click="showCampaignModelDatils"
            >新增投放</el-button
          >
          <el-table
            :data="AdCampaignShowData.mMAdCampaigns"
            style="width: 100%; height: 200px"
          >
            <el-table-column
              prop="CampaignID"
              label="投放任务编码"
              width="180"
            />
            <el-table-column prop="CampaignName" label="任务名称" width="180" />
            <el-table-column label="开始时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.StartTime) }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.EndTime) }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="removeCampaignbtn(scope.$index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="dialogAdCampaignVisible = false">取消</el-button>
        <el-button type="primary" @click="SubmitBdAdCampaign">确认</el-button>
      </template>
    </el-dialog>
    <!-- 选择投放任务的信息框 -->
    <el-dialog
      v-model="CampaigndialogVisible"
      title="投放任务信息"
      width="500px"
    >
      <div class="mt-4">
        <el-button type="primary" @click="revertValueBtn">返回</el-button>
        <el-input
          v-model="searchCampaignKeyword"
          style="width: 200px"
          placeholder="请输入投放任务名称"
          class="input-with-select ml-2"
        >
          <template #append>
            <el-button @click="QueryCampaignBtn" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="投放任务列表" name="AdCampaignlist">
          <el-table
            :data="AdCampaignShowData2"
            row-key="CampaignID"
            style="width: 100%; height: 500px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60" />
            <el-table-column
              prop="CampaignID"
              label="投放任务编码"
              width="180"
            />
            <el-table-column
              prop="CampaignName"
              label="投放任务名称"
              width="180"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- 投放任务列表分页 -->
      <el-pagination
        v-model:current-page="AdCampaignpagination.current"
        v-model:page-size="AdCampaignpagination.Rows"
        size="small"
        background
        layout="prev, pager, next"
        :total="AdCampaignpagination.Records"
        @size-change="handleCampaignPaginationChange"
        @current-change="handleCampaignPaginationChange"
        class="mt-4 ml-14"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import {
  Search,
  Plus,
  Edit,
  Delete,
  Key,
  Clock,
  Cpu
} from "@element-plus/icons-vue";
import axios from "axios";
import { ElMessage } from "element-plus";
import {
  getAsyncDevice,
  AddAsyncDevice,
  UpdateAsyncDevice,
  DeleteAsyncDevice,
  GetByIdAsyncDevice,
  UpdateLaunchCampaignAdCampaign
} from "@/api/materialManage/device";
import { getAsyncAdCampaign } from "@/api/materialManage/program.ts";

// 设备数据相关
const loading = ref(false);
const modelList = ref([]);
const searchKeyword = ref("");
//分页
var pagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});
//投放任务分页
var AdCampaignpagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});

// 对话框相关
const dialogVisible = ref(false);
const CampaigndialogVisible = ref(false);
const searchCampaignKeyword = ref("");
const selectedRows = ref([]); // 存储选中行数据
// 对话框相关
const dialogAdCampaignVisible = ref(false);
const dialogType = ref("add");
const formData = reactive({
  DeviceID: 0,
  DeviceName: "",
  DeviceModel: ""
  // DeviceOrientation: "",
  // DeviceHeight: 0,
  // DeviceWidth: 0,
  // DeviceResolution: "",
  // StoreId: 0,
  // OnlineStatusCode: 1
});
const AdCampaignShowData = ref({});
const AdCampaignShowData2 = ref({});
const activeName = ref("AdCampaignlist");

// 表单验证规则
const formRules = {
  DeviceName: [
    { required: true, message: "请输入设备名称", trigger: "blur" },
    { min: 2, max: 20, message: "长度2-20个字符", trigger: "blur" }
  ]
  // DeviceModel: [
  //   { required: true, message: "请输入设备型号", trigger: "blur" },
  //   { min: 2, max: 20, message: "长度2-20个字符", trigger: "blur" }
  //   //{ pattern: /^[A-Z0-9]{6,10}$/, message: "格式不正确", trigger: "blur" }
  // ]
};
//保存投放设备
const SubmitBdAdCampaign = async () => {
  const UpdateLaunchAdCampaign = {
    DeviceID: AdCampaignShowData.value.DeviceID,
    DeviceName: AdCampaignShowData.value.DeviceName,
    mMPlaybackDevice: AdCampaignShowData.value.mMAdCampaigns.map(item => ({
      CampaignID: item.CampaignID,
      DeviceID: AdCampaignShowData.value.DeviceID
    }))
  };
  var response = await UpdateLaunchCampaignAdCampaign(UpdateLaunchAdCampaign);
  ElMessage.success(response.message);
  dialogAdCampaignVisible.value = false;
};
// 显示设备绑定投放任务详情
const showModelDatils = async id => {
  const deleteDevice = {
    DeviceID: id
  };
  var response = await GetByIdAsyncDevice(deleteDevice);
  if (response.data != null) {
    AdCampaignShowData.value = response.data;
    dialogAdCampaignVisible.value = true;
  }
};
// 设备分页处理
const handleCampaignPaginationChange = () => {
  showCampaignModelDatils();
};
// 复选框变化时更新选中数据
const handleSelectionChange = val => {
  selectedRows.value = val;
};
//移除投放任务
const removeCampaignbtn = index => {
  AdCampaignShowData.value.mMAdCampaigns.splice(index, 1);
};
//点击返回按钮返回设备数据
const revertValueBtn = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.warning("未选择信息返回！");
    return;
  }
  // 深拷贝选中的数据
  const copiedData = JSON.parse(JSON.stringify(selectedRows.value));

  // 合并到表格中（避免重复）
  const existingIds = AdCampaignShowData.value.mMAdCampaigns.map(
    item => item.CampaignID
  );
  const newData = copiedData.filter(
    item => !existingIds.includes(item.CampaignID)
  );

  AdCampaignShowData.value.mMAdCampaigns = [
    ...AdCampaignShowData.value.mMAdCampaigns,
    ...newData
  ];
  CampaigndialogVisible.value = false;
  selectedRows.value = [];
};
//点击按钮查询设备
const QueryCampaignBtn = () => {
  showCampaignModelDatils();
};
// 显示投放详情
const showCampaignModelDatils = async () => {
  //编写参数对象
  const params = {
    Status: "1",
    AllDates: new Date(Date.now()),
    QueryCriteria: searchCampaignKeyword.value,
    Rows: AdCampaignpagination.Rows,
    Page: AdCampaignpagination.current,
    Sidx: "CampaignID",
    Sord: "desc"
  };
  var response = await getAsyncAdCampaign(params);
  AdCampaignShowData2.value = response.data.list;
  AdCampaignpagination = response.data.paging;
  CampaigndialogVisible.value = true;
};

// 获取设备列表
const fetchDevices = async () => {
  loading.value = true;
  try {
    //编写参数对象
    const params = {
      Status: "1",
      AllDates: new Date(Date.now()),
      QueryCriteria: searchKeyword.value,
      Rows: pagination.Rows,
      Page: pagination.current,
      Sidx: "DeviceID",
      Sord: "desc"
    };
    // 调用设备接口方法getAsyncDevice，并将参数传入
    const response = await getAsyncDevice(params);
    modelList.value = response.data.list;
    pagination = response.data.paging;
  } finally {
    loading.value = false;
  }
};

// 显示对话框
const showDialog = (type, device) => {
  dialogType.value = type;
  if (type === "edit") {
    Object.assign(formData, device);
  } else {
    formData.DeviceID = 0;
    formData.DeviceName = "";
    formData.DeviceModel = "";
    // formData.DeviceOrientation = "";
    // formData.DeviceHeight = 0;
    // formData.DeviceWidth = 0;
    // formData.DeviceResolution = "";
    // formData.StoreId = 0;
  }
  dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  // 提交操作
  if (dialogType.value === "add") {
    const newDevice = {
      Model: {
        DeviceID: 0,
        DeviceName: formData.DeviceName,
        DeviceModel: formData.DeviceModel
        // DeviceOrientation: formData.DeviceOrientation,
        // DeviceHeight: formData.DeviceHeight,
        // DeviceWidth: formData.DeviceWidth,
        // DeviceResolution: formData.DeviceResolution,
        // StoreId: 0,
        // OnlineStatusCode: 1
      }
    };
    var response = await AddAsyncDevice(newDevice);
    dialogVisible.value = false;
    ElMessage.success(response.message);
    await fetchDevices();
  } else {
    //修改操作
    const updateDevice = {
      Model: {
        DeviceID: formData.DeviceID,
        DeviceName: formData.DeviceName,
        DeviceModel: formData.DeviceModel
        // DeviceOrientation: formData.DeviceOrientation,
        // DeviceHeight: formData.DeviceHeight,
        // DeviceWidth: formData.DeviceWidth,
        // DeviceResolution: formData.DeviceResolution,
        // StoreId: 0,
        // OnlineStatusCode: 1
      }
    };
    var response = await UpdateAsyncDevice(updateDevice);
    dialogVisible.value = false;
    ElMessage.success(response.message);
    await fetchDevices();
  }
};

// 删除设备
const deleteDevice = async id => {
  const deleteDevice = {
    DeviceID: id
  };
  var response = await DeleteAsyncDevice(deleteDevice);
  await fetchDevices();
  ElMessage.success(response.message);
};

// 分页处理
const handlePaginationChange = () => {
  fetchDevices();
};

// 日期格式化
const formatDate = date => {
  return new Date(date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 初始化加载
onMounted(fetchDevices);
</script>

<style scoped lang="scss">
.class-management {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;

  .operation-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }
  }

  .class-list {
    margin-bottom: 20px;
    height: 85%;
  }

  .class-card {
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .class-header {
      display: flex;
      align-items: center;
      gap: 10px;

      .title-name {
        font-weight: bold;
        font-size: 16px;
      }
    }

    .card-content {
      padding: 10px 0;

      .info-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
          color: #666;
        }

        .label {
          color: #666;
          margin-right: 5px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;

    .search-input {
      width: 100% !important;
    }
  }
}
</style>
