import { ref } from "vue";
// 公用方法 
let instance = null;
export function utils() {

  if (!instance) {
    const addFoodManangeTable = ref<any[]>([]);
    
    // 格式化表格数据方法
    function formattedDate(row: any) {
      return row
      .replace(/原:/g, "<br/>原:<br/>")
      .replace(/新:/g, "<br/>新:<br/>")
      .replace(/,/g, "<br/>");
    }
    // 新增表单的方法
    const form = ref({
      Name:"",
      FdCName: "",
      MarketPrice: "",
      SalePrice: "",
      PriceMode: "",
      delivery: false,
      ApplicableStores: [],
      FtNo: ""
    });
    // 新增套餐商品里面表格的数据
    const addPackageTableData = ref([
      {
        PGFdCName: "",
        CommodityPrice: "",
        PGFdQty: 1,
        PGFdNo: null
      }
    ]);
    // 用户新增商品还是修改商品
    const isAdd = ref(true);
    const isView = ref([])
    const filteredData = ref([])
 const adjustTableData = ref([]);
    // 生成表格中数据id
const generateUniqueId = (prefix = 'item') => {
  const timestamp = Date.now().toString();
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}_${timestamp}_${randomNum}`;
    };
    
    // 创建实例
    instance = {
      formattedDate,
      addFoodManangeTable,
      form,
      addPackageTableData,
      isAdd,
      isView,
      generateUniqueId,
      filteredData,
     adjustTableData
    };
  }

  return instance;
}
