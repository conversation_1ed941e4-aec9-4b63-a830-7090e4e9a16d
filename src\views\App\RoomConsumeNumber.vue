<template>
  <div class="query-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="hover" class="filter-card">
      <el-form :model="filterForm" :inline="!isMobile" class="filter-form">
        <el-form-item label="区域">
          <el-select
            style="width: 100px"
            v-model="filterForm.ShopID"
            placeholder="选择区域"
            clearable
          >
            <el-option
              v-for="item in Regionlist"
              :key="item.AreaID"
              :label="item.AreaName"
              :value="item.AreaID"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="action-buttons">
          <el-button type="primary" @click="fetchData">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 结果列表卡片 -->
    <el-card shadow="hover" class="result-card">
      <el-table
        :data="ModelData"
        border
        stripe
        v-loading="loading"
        height="450px"
      >
        <el-table-column prop="RmNo" label="房间号" width="120" />
        <el-table-column prop="FdQty" label="消费在线人数" min-width="150" />
      </el-table>

      <el-pagination
        size="small"
        background
        layout="prev, pager, next,total"
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.Rows"
        :total="pagination.Records"
        @size-change="handlePaginationChange"
        @current-change="handlePaginationChange"
        class="mt-4"
      />
      <!--
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.Rows"
        :total="pagination.Records"
        size="small"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next"
        @size-change="handlePaginationChange"
        @current-change="handlePaginationChange"
      />-->
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import {
  getAsyncDbFoodRoom,
  getAsyncDbFoodRoomRegion
} from "@/api/DbFoodRoom/RoomConsumeNumber";

// 响应式布局处理
const isMobile = ref(window.innerWidth < 768);
const tableHeight = ref("auto");

const handleResize = () => {
  isMobile.value = window.innerWidth < 768;
  tableHeight.value = isMobile.value ? "400px" : "auto";
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  handleResize();
  fetchRegionData();
  fetchData();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

// 筛选表单
const filterForm = ref({
  ShopID: null
  // keyword: "",
  // status: "",
  // dateRange: []
});

// 分页配置
var pagination = reactive({
  current: 1,
  Rows: 20,
  Total: 0,
  Records: 0
});

// 数据状态
const loading = ref(false);
const ModelData = ref([]);
const Regionlist = ref([]);
// 获取区域数据
const fetchRegionData = async () => {
  //编写参数对象
  const params = {
    ShopId: 5
  };
  const response = await getAsyncDbFoodRoomRegion(params);
  Regionlist.value = response.data;
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    //编写参数对象
    const params = {
      // Status: "1",
      // AllDates: new Date(Date.now()),
      AreaID: filterForm.value.ShopID,
      ShopID: 5,
      Rows: pagination.Rows,
      Page: pagination.current,
      Sidx: "RmNo",
      Sord: "desc"
    };
    if (filterForm.value.ShopID == null) {
      return;
    }
    const response = await getAsyncDbFoodRoom(params);
    ModelData.value = response.data.list;
    pagination = response.data.paging;
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handlePaginationChange = async () => {
  await fetchData();
};
</script>

<style scoped>
.query-container {
  padding: 16px;
}

.filter-card {
  margin-bottom: 16px;
}

.result-card {
  margin-bottom: 16px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.action-buttons {
  margin-left: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }

  .action-buttons {
    margin-left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .el-form-item {
    width: 100%;
    margin-right: 0;
  }

  .el-date-editor {
    width: 100%;
  }
}
</style>
