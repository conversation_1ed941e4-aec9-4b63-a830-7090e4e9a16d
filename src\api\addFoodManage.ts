import { http} from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
export const GetCommExistingGoods = (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommExistingGoods", { params },
  );
};

// 保存到草稿
export const AddOrUpdateCommDraftRecords = (data?: object) => {
  return http.request<any>("post", "/CommodityAdjustment/AddOrUpdateCommDraftRecords", { data },
  );
};

// 查询个人草稿详情记录
export const GetCommDraftListRecord = (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommDraftListRecord", { params },
  );
};

// 查询历史调价记录 已有商品调价记录
export const GetCommHistoricalPrice = (params?: object) => {
  return http.request<any>("get", "/CommodityAdjustment/GetCommHistoricalPrice", { params },
  );
};

// 同步门店
export const CommSynchronousStore = (data?: object) => {
  return http.request<any>("post", "/CommodityAdjustment/CommSynchronousStore", { data },
  );
};

// 保存到草稿页面