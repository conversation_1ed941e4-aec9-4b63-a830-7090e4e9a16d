import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//素材管理接口
//查询所有素材信息的接口
export const getAsyncFile = (params) => {
  return http.request<any>("get", "/api/MMFile/GetAll",{params});
};
//上传素材的接口
export const UploadAsyncFile = (data) => {
  return http.request<any>("post", "/api/MMFile/Upload", { data }, {
      headers: {
        // 自动设置 multipart/form-data boundary
        'Content-Type': 'multipart/form-data'  
      }
  });
};
//修改素材的接口（暂无需调用）
export const UpdateAsyncFile = (data) => {
  return http.request<any>("put", "/api/MMFile/Update",{data});
};
//删除素材的接口
export const DeleteAsyncFile = (data) => {
  return http.request<any>("delete", "/api/MMFile/Delete",{data});
};
