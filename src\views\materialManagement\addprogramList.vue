<template>
  <div>
    <!-- <div style="display: flex; justify-content: flex-end; align-items: center">
      <el-input
        style="margin-right: 10px; width: 200px"
        v-model="bodyMsg"
        placeholder="请搜索节目名称"
      ></el-input>
      <el-button>搜索</el-button>
      <el-button @click="newProgram">新建投放</el-button>
    </div>
    <el-card style="max-width: 480px">
      <p v-for="o in 4" :key="o" class="text item">页面显示</p>
      <template #footer>
        <div
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <el-button>删除节目</el-button>
          <el-button>结束投放</el-button>
          <el-button>播放设备</el-button>
          <el-button type="primary">编辑节目</el-button>
        </div>
      </template>
    </el-card> -->
    <!-- 新建投放任务弹窗 -->
    <el-dialog
    v-model="dialogFormVisible"
    width="100%"
    style="height:100%; margin:0px; padding-top:20px;"

    @close="handleDialogClose"
    >
      <!-- <div style="width: 100%; height:100%;"> -->
        <div
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <el-button type="primary" @click="launch">投放</el-button>
        </div>
        <el-tabs
          tab-position="left"
          style="height: 100%"
          class="demo-tabs"
          @update:modelValue="changeChoose"
        >
          <el-tab-pane label="选择画布">
            <el-tabs
              v-model="chooseCanvas"
              tab-position="top"
              style="height: 100%"
              class="demo-tabs"
              @update:modelValue="handleTabChange"
            >
              <el-tab-pane name="1" label="横向" class="p-0 m-0">
                <div class="aspect-ratio-16-9">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="baseUrlApi(list[0].url)"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="2" label="纵向">
                <div class="aspect-ratio-9-16">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="list[0].url"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="选择布局" name="chooseLayout">
            <el-tabs
              class="demo-tabs"
              v-model="currentLayoutId"
              @update:modelValue="handleLayoutChange"
            >
              <!-- 动态生成布局选项卡 -->
              <el-tab-pane
                v-for="layoutItem in layoutDemo"
                :key="layoutItem.id"
                :label="layoutItem.name"
                :name="layoutItem.id"
              >
                <!-- 动态生成 GridStack 容器 -->
                <div
                  :class="`aspect-ratio-${chooseCanvas === '1' ? '16-9' : '9-16'}`"
                >
                  <div
                    :id="`grid-stack-${layoutItem.id}`"
                    class="grid-stack"
                    style="
                      border: 2px dashed lightgrey;
                      width: 100%;
                      height: 100%;
                    "
                  ></div>
                </div>
                <!-- </el-tab-pane>
      </el-tabs> -->
                <!-- <el-button type="primary" @click="chooseMaterial">选择素材</el-button> -->
                <div
                  style="
                    width: 100%;
                    max-height: 260px;
                    border: 1px solid gray;
                    margin-top: 15px;
                    padding: 10px;
                  "
                >
                  <el-scrollbar height="250px">
                    <p v-if="savueSelectFile.length == 0">您已选择的素材会展示在这里</p>
                    <el-row :gutter="10">
                      <el-col v-for="item in savueSelectFile" :span="2" style="height:200px; margin-right:40px; width: 100%;">
                        <img
                          v-if="item.FormatType.includes('image')"
                          :src="baseUrlApi(item.FilePath)"
                          style="width: 100%; height: 100%"
                        />
                        <video
                          v-if="item.FormatType.includes('mp4')"
                          controls
                          :src="baseUrlApi(item.FilePath)"
                          style="width: 100%; height: 100%"
                        ></video>
                        <audio
                          v-if="item.FormatType.includes('mp3')"
                          controls
                          :src="baseUrlApi(item.FilePath)"
                          style="width: 100%; height: 100%"
                        ></audio>
                        <el-button style="width: 50px; margin: 0px 15px;" @click="delectSelectFile(item)">删除</el-button>
                      </el-col>
                    </el-row>
                  </el-scrollbar>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
        </el-tabs>
      <!-- </div> -->
      <!-- </div> -->
    </el-dialog>
    <!-- 投放按钮弹窗 -->
    <el-dialog v-model="launchDialog" title="投放信息" width="600" draggable>
      <!-- 添加弹性容器包裹卡片 -->
      <el-form
        :model="lanchFrom"
        label-width="auto"
        style="max-width: 500px"
        :rules="rules"
      >
        <el-form-item label="投放名称" prop="name">
          <el-input v-model="lanchFrom.name" autocomplete="off" />
        </el-form-item>
        <el-form-item
          label="投放时间"
          placeholder="请选择投放时间"
          prop="lanchRespect"
        >
          <el-radio-group v-model="lanchFrom.lanchtime">
            <!-- <el-radio value="All">长期投放</el-radio> -->
            <el-radio value="All">定时投放</el-radio>
          </el-radio-group>
          <el-form-item>
            <el-col :span="11">
              <el-date-picker
                v-model="lanchFrom.putBegin"
                type="datetime"
                placeholder="选择开始投放时间"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">-</span>
            </el-col>
            <el-col :span="11">
              <el-date-picker
                type="datetime"
                v-model="lanchFrom.putEnd"
                placeholder="选择结束投放时间"
                style="width: 100%"
              />
            </el-col>
          </el-form-item>
          <!-- <el-select v-model="lanchFrom.lanchRespect">
            <el-option label="每天重复" value="shanghai" />
            <el-option label="每周重复" value="beijing" />
            <el-option label="每年重复" value="beijing" />
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="全天播放">
          <el-switch v-model="lanchFrom.allDay" />
        </el-form-item> -->
        <!-- <el-form-item label="播放时间" v-if="lanchFrom.allDay === false">
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.beginTime"
              type="date"
              placeholder="选择开始播放时间"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.endTime"
              placeholder="选择结束播放时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item> -->
        <el-form-item label="投放设备">
          <el-button @click="lanchEquipment">选择投放设备</el-button>
        </el-form-item>
        <el-form-item label="已选择设备" v-if="selectEquiment.length > 0">
          <span
            v-if="selectEquiment.length > 0"
            v-for="item in selectEquiment"
            style="margin: 0 10px; color: gray"
          >
            {{ item.DeviceName }}
          </span>
        </el-form-item>
      </el-form>

      <el-dialog
        v-model="equipmentDialog"
        width="1000"
        draggable
        title="选择播放设备"
        append-to-body
        aligin-center="true"
        style="max-height: 600px"
      >
        <el-scrollbar height="400px">
          <!-- <div class="material-container"> -->
          <el-row :gutter="4">
            <el-col
              :span="12"
              v-for="item in equipmentList"
              style="margin-bottom: 10px"
            >
              <el-row>
                <el-col :span="2">
                  <el-checkbox
                    v-model="item.selected"
                    :true-label="item.DeviceID"
                    @change="handleSelectEquipment(item)"
                    style="margin-left: 10px"
                  ></el-checkbox>
                </el-col>
                <el-col :span="22">
                  <el-card
                    :key="item.DeviceID"
                    style="
                      max-width: 350px;
                      height: 150px;
                      display: flex;
                      align-items: center;
                      gap: 20px;
                    "
                  >
                    <img
                      src="@/assets/equiment.png"
                      style="height: 100px; flex-shrink: 0; margin-right: 30px"
                    />
                    <div style="text-align: left; float: right">
                      <p>
                        名称：<span class="equMsg">{{ item.DeviceName }}</span>
                      </p>
                      <p>
                        型号：<span class="equMsg">{{ item.DeviceModel }}</span>
                      </p>
                      <p>
                        尺寸：<span class="equMsg">{{
                          item.DeviceResolution
                        }}</span>
                      </p>
                      <p>
                        形状：<span class="equMsg">{{
                          item.DeviceOrientation
                        }}</span>
                      </p>
                      <P
                        >状态：<span class="equMsg"
                          ><el-tag
                            v-if="item.OnlineStatus.StatusID === 2"
                            :key="item.DeviceID"
                            type="info"
                            effect="light"
                            round
                          >
                            {{ item.OnlineStatus.StatusName }}
                          </el-tag>
                          <el-tag
                            v-if="item.OnlineStatus.StatusID === 1"
                            :key="item.DeviceID"
                            type="success"
                            effect="light"
                            round
                          >
                            {{ item.OnlineStatus.StatusName }}
                          </el-tag>
                        </span></P
                      >
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <!-- </div> -->
        </el-scrollbar>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="equipmentDialog = false">取消</el-button>
            <el-button type="primary" @click="sureSelectEquipment">
              确定选择
            </el-button>
          </div>
        </template>
      </el-dialog>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="launchDialog = false">取消</el-button>
          <el-button type="primary" @click="addPutMark"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 素材选择弹窗 -->
    <el-dialog
      v-model="programDialog"
      title="素材选择"
      width="1000"
      draggable
      style="max-height: 800px"
    >
      <el-scrollbar height="400px">
        <!-- 添加弹性容器包裹卡片 -->
        <div class="material-container">
          <el-card
            v-for="item in materialList"
            :key="item.id"
            class="material-card"
            style="max-width: 300px"
          >
            <!-- 添加多选框 -->
            <div class="card-header">
              <el-checkbox
                v-model="item.selected"
                :true-label="item.id"
                @change="handleSelectionChange(item)"
              ></el-checkbox>
            </div>
            <!-- 图片区域 -->
            <div class="card-image">
              <img
                v-if="item.FormatType.includes('image')"
                :src="baseUrlApi(item.FilePath)"
                style="width: 100%"
              />
              <video
                v-if="item.FormatType.includes('mp4')"
                controls
                :src="baseUrlApi(item.FilePath)"
                style="width: 100%"
              ></video>
              <audio
                v-if="item.FormatType.includes('mp3')"
                controls
                :src="baseUrlApi(item.FilePath)"
                style="width: 100%"
              ></audio>
            </div>
          </el-card>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSelectionChange">取消</el-button>
          <el-button type="primary" @click="getchooseList"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { Delete, Download, Plus, ZoomIn } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getMaterial, getEquipment, addPut } from "@/api/programList";
import { onMounted, watch, ref, nextTick, reactive,toRefs} from "vue";
import { GridStack } from "gridstack";
import { baseUrlApi } from "@/api/util.ts";
import "gridstack/dist/gridstack.min.css";
// 接收父组件的值
const props = defineProps({
  show: Boolean,
  changeData: Object,
});

// 解构后在模板中可以直接使用 `show` 和 `changeData`
const { show, changeData } = toRefs(props);
const bodyMsg = ref("");
const grid = ref(null); // 统一使用这个ref管理实例
const gridStackRef = ref(null);
const gridData = [];
const addWidgetContent = ref(0);
const dialogFormVisible = ref(show);
const dialogVisible = ref(show);
const chooseCanvas = ref("1"); //选择画布
const dialogImg = ref(false); //图片缩略图弹窗
const dialogImageUrl = ref(""); //图片缩略图弹窗地址
const list = ref([]);
const imgUrl = ref("");
const programDialog = ref(false); //素材弹窗
const materialList = ref([]);
const launchDialog = ref(false); //点击投放按钮后的弹窗
const currentLayoutId = ref("0"); // 当前选中的主布局（二分屏/三分屏）
const currentOrientation = ref("横屏"); // 当前选中的方向（横屏/竖屏）
const gridInstances = ref({}); // 存储各布局的 GridStack 实例
const layoutDemo = ref([
  {
    id: "0",
    name: "默认屏幕",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 6, content: "素材1", id: "one-row" }
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 6, content: "素材1", id: "one-column" }
        ]
      }
    ]
  },
  {
    id: "1",
    name: "二分屏",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 6, h: 6, content: "素材1", id: "two-row-1" },
          { x: 6, y: 0, w: 6, h: 6, content: "素材2", id: "two-row-2" }
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 3, content: "上面内容", id: "two-column-1" },
          { x: 0, y: 6, w: 12, h: 3, content: "下面内容", id: "two-column-2" }
        ]
      }
    ]
  },
  {
    id: "2",
    name: "三分屏",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 4, h: 6, content: "素材1", id: "three-row-1" },
          { x: 4, y: 0, w: 4, h: 6, content: "素材2", id: "three-row-2" },
          { x: 8, y: 0, w: 4, h: 6, content: "素材3", id: "three-row-3" }
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          {
            x: 0,
            y: 0,
            w: 12,
            h: 2,
            content: "上面内容",
            id: "three-column-1"
          },
          {
            x: 0,
            y: 2,
            w: 12,
            h: 2,
            content: "中间内容",
            id: "three-column-2"
          }, // 修正坐标
          { x: 0, y: 4, w: 12, h: 2, content: "下面内容", id: "three-column-3" } // 修正坐标
        ]
      }
    ]
  }
]);

const newProgram = () => {
  dialogFormVisible.value = true;
};
const handleTabChange = newTab => {
  console.log("当前选中的画布:", chooseCanvas.value);
  console.log("画布方向已切换:", newTab);
  chooseCanvas.value = newTab;

  // 如果已有选中的布局，重新应用当前布局以匹配新方向
  if (currentLayoutId.value) {
    handleLayoutChange(currentLayoutId.value);
  }
};
const changeChoose = newValue => {
  console.log(newValue);
  if (newValue == "chooseLayout") {
    // 当标签值是选择布局的时候调用初始化布局方法
    initLayout();
  }
}; // 默认选中第一个布局

// 初始化布局方法
const initLayout = async () => {
  if (currentLayoutId.value && layoutDemo.value.length > 0) {
    await handleLayoutChange(currentLayoutId.value);
  }
};

const handleLayoutChange = async newLayoutId => {
  savueSelectFile.value = []; // 清空素材列表
  console.log("当前选中的布局ID:", newLayoutId);
  // 初始化新布局的 GridStack
  const layoutItem = layoutDemo.value.find(item => item.id === newLayoutId);
  console.log("当前选中的布局:", layoutItem);
  await nextTick();
  layoutItem.layout.forEach(orientation => {
    console.log("当前选中的布局:", orientation);
    const gridId = `grid-stack-${newLayoutId}`;
    const gridRef = document.getElementById(gridId);
    const isMatchingOrientation =
      (chooseCanvas.value === "1" && orientation.name === "横屏") ||
      (chooseCanvas.value === "2" && orientation.name === "竖屏");
    if (!isMatchingOrientation) return; // 跳过不匹配的方向
    if (gridRef) {
      console.log("高度:", gridRef.clientHeight);
      const options = {
        margin: 0,
        cellHeight: gridRef.clientHeight / 6,
        column: 12,
        maxRow: 6,
        disableDrag: true,
        disableResize: true,
        animate: false
      };
      // 定义点击事件处理函数
      GridStack.renderCB = (el, node) => {
        // el.innerHTML = node.content || "点击选择素材";
        // 设置初始内容
        const widgetMaterial = savueSelectFile.value.find(m => m.widgetId === node.widgetIndex);
        console.log("当前选择的素材:", widgetMaterial);
            if (widgetMaterial) {
              // 如果有已选择的素材，显示素材预览
              if (widgetMaterial.FormatType.includes('image')) {
                el.innerHTML = `<img src="${baseUrlApi(widgetMaterial.FilePath)}" alt="素材预览" class="w-full h-full object-cover">`;
              } else if (widgetMaterial.FormatType.includes('mp4')) {
                el.innerHTML = `<video controls src="${baseUrlApi(widgetMaterial.FilePath)}" class="w-full h-full object-cover"></video>`;
              } else if (widgetMaterial.FormatType.includes('mp3')) {
                el.innerHTML = `<audio controls src="${baseUrlApi(widgetMaterial.FilePath)}" class="w-full h-full"></audio>`;
              } else {
                el.innerHTML = widgetMaterial.Name || '未知类型素材';
              }
            } else {
              // 没有选择素材，显示默认提示
              el.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">
                <i class="fa fa-plus-circle mr-2"></i>点击选择素材
              </div>`;
            }
        el.dataset.widgetIndex = node.widgetIndex; // 添加数据属性
        // 监听鼠标悬停方法
        el.addEventListener("click", () => {
          console.log("当前选中的布局:", node.widgetIndex);
          chooseMaterial(node.widgetIndex);
        });
        el.classList.add('transition-all', 'duration-300');
        el.addEventListener("mouseover", () => {
          el.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');
            });
            el.addEventListener('mouseleave', () => {
              el.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
            });
      };
      const instance = GridStack.init(options, gridRef);
      orientation.layoutList.forEach(widgetConfig => {
        console.log("当前选中的布局:", widgetConfig);
        instance.addWidget({
          ...widgetConfig,
          content: "点击选择素材",
          widgetIndex: widgetConfig.id
        });
      });
      //       gridRef.addEventListener("click", (e) => {
      //         console.log("点击事件:", e.target.closest('[data-index]')?.dataset.index);
      //         const widgetIndex = e.target.closest('[data-index]')?.dataset.index;
      //         console.log("当前选中的布局:", widgetIndex);
      //         if (widgetIndex !== undefined) {
      //           handleWidgetClick(Number(widgetIndex));
      //   }
      // });
      // widget1.addEventListener("click", () => handleWidgetClick(0));
      // 存储实例以便后续操作
      gridInstances.value[gridId] = instance;
      console.log("当前布局:", gridInstances.value[gridId]);
    }
  });
  // 更新当前选中的布局ID
  currentLayoutId.value = newLayoutId;
};
const widgetSelections = ref([]);
// 更新素材信息
const updateWidgetContent = (widgetIndex, material) => {
      const gridId = `grid-stack-${currentLayoutId.value}`;
      console.log(gridId)
      const grid = gridInstances.value[gridId];
      console.log(gridInstances.value[gridId])
      
      if (grid) {
        // 找到对应的widget
        const widget = grid.engine.nodes.find(node => node.widgetIndex === widgetIndex);
        console.log(material)
        if (widget) {
          // 更新widget内容
          const el = document.querySelector(`[data-widget-index="${widgetIndex}"]`);
          console.log(el);
          if (el) {
            if(material !== null && material.length > 0){
              console.log( "zouzhe")
              for (var i = 0; i < material.length; i++){
              if (material[0].FormatType.includes('image')) {
              el.innerHTML = `<img src="${baseUrlApi(material[0].FilePath)}" alt="${material[0].Name}" class="w-full h-full object-cover transition-opacity duration-500">`;
            } else if (material[0].FormatType.includes('mp4')) {
              el.innerHTML = `<video controls src="${baseUrlApi(material[0].FilePath)}" class="w-full h-full object-cover"></video>`;
            } else if (material[0].FormatType.includes('mp3')) {
              el.innerHTML = `<audio controls src="${baseUrlApi(material[0].FilePath)}" class="w-full h-full"></audio>`;
            } else {
              el.innerHTML = material.Name || '未知类型素材';
            }
            }
            }else{
              el.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">
                <i class="fa fa-plus-circle mr-2"></i>点击选择素材
              </div>`;
              }    
}
          
        }
      }
    };
const handleWidgetClick = widgetIndex => {
  console.log("点击的小部件索引:", widgetIndex);
  // 示例：打开素材选择弹窗并传递当前小部件索引
  openMaterialDialog(widgetIndex);
};

const openMaterialDialog = targetIndex => {
  // 记录当前目标小部件索引，用于后续赋值
  selectedWidgetIndex.value = targetIndex;
  // 打开弹窗
  programDialog.value = true;
};
// const autoGridStackInit = () => {
//   const autoCridStack1 = document.querySelector("#grid-stack-auto-row");
//   const autoGridStack2 = document.querySelector("#grid-stack-auto-coloumn");
//   // 确保DOM已经渲染
//   if (!autoCridStack1 && !autoGridStack2) {
//     console.error("GridStack容器未找到");
//     return;
//   }

//   const renderLayout = (gridStackRef, layout) => {
//     const containerHeight = gridStackRef.clientHeight;
//     console.log(containerHeight);
//     const options = {
//       dragOut: true,
//       margin: 0,
//       allowHtml: true,
//       cellHeight: containerHeight / 6,
//       column: 12,
//       maxRow: 6,
//       maxHeight: containerHeight,
//       minHeight: 6,
//       animate: false,
//       acceptWidgets: true, //接受从其他网格或外部拖动的小部件
//       dragIn: ".add-grid", //可以从外部拖动的类
//       dragInOptions: {
//         revert: "invalid",
//         scroll: false, //当元素被拖动到网格的底部或顶部时启用或禁用滚动
//         appendTo: "body", //添加到body中
//         helper: "clone" //放置时的辅助函数=>克隆
//       }, //可以从外部拖动类的配置
//       removable: "#trash", //在拖动到网格外时删除小部件的类
//       removeTimeout: 100 //在拖动到网格外时删除小部件之前的时间 100毫
//     };
//     GridStack.renderCB = (el, node) => {
//       el.innerHTML = node.content || "";
//     };
//     grid.value = GridStack.init(options, gridStackRef);

//     // 添加拖拽事件监听
//     const draggableItems = document.querySelectorAll(".add-grid");
//     draggableItems.forEach(item => {
//       item.addEventListener("dragstart", e => {
//         // 设置拖拽数据，可以包含小部件的配置信息
//         e.dataTransfer.setData(
//           "text/plain",
//           JSON.stringify({
//             content: `<div class="widget-content">自定义小部件</div>`,
//             w: 4, // 默认宽度
//             h: 2 // 默认高度
//           })
//         );
//       });
//     });

//     // 处理从外部拖入的事件
//     grid.value.on("added", (e, items) => {
//       console.log("添加了新小部件:", items);
//       // 这里可以添加自定义逻辑，例如设置小部件的样式或内容
//     });
//     if (grid.value) {
//       if (layout === "autoRow") {
//         grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       } else if (layout === "autoColumn") {
//         grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       }
//     }
//   };
//   if (autoCridStack1) {
//     renderLayout(autoCridStack1, "autoRow");
//   }
//   if (autoGridStack2) {
//     renderLayout(autoGridStack2, "autoColumn");
//   }
// };
const selectdItem = ref([]);
const handleSelectionChange = item => {
  if (item.selected) {
    selectdItem.value.push(item);
  } else {
    selectdItem.value = selectdItem.value.filter(i => i.FileID !== item.FileID);
  }
};
const closeSelectionChange = item => {
  item.selected = false;
  programDialog.value = false;
};
const savueSelectFile =ref([]);
const getchooseList = () => {
  // 检查是否有有效的布局 ID（避免未选择布局时出错）
  if (!selectedLayoutId.value) {
    console.warn("未选择布局，无法绑定 ID");
    return;
  }
  // 过滤出选中的文件，并绑定对应的 layoutId
  const selectedFiles = materialList.value
    .filter(item => item.selected) // 筛选已选中的文件
    .map(file => ({
      ...file,
      layoutId: selectedLayoutId.value // 使用当前弹窗的布局 ID
    }));
    console.log(selectdItem.value)
      // 更新GridStack中的widget内容
  updateWidgetContent(selectedLayoutId.value, selectedFiles); 
  // 更新全局选中列表（可选：合并或替换原有数据）
  savueSelectFile.value = [...savueSelectFile.value,...selectedFiles]; // 直接赋值为当前布局的选中文件
  // 或合并到现有列表：selectdItem.value = [...selectdItem.value, ...selectedFiles];
  console.log("绑定后的文件数据：", savueSelectFile.value);
  programDialog.value = false;
};

// 素材展示之删除按钮方法
const delectSelectFile = (item) => {
  console.log(item);
  const index = savueSelectFile.value.findIndex(i => i.FileID === item.FileID);
  if (index!== -1) {
    // 获取要删除的item的widgetIndex
    const widgetIndex = savueSelectFile.value[index].layoutId;
    savueSelectFile.value = savueSelectFile.value.filter(i => i.FileID!== item.FileID);
    console.log(savueSelectFile.value);
    console.log("删除后的文件数据：", savueSelectFile.value);
    updateWidgetContent(widgetIndex, savueSelectFile.value);
  }
};
const addGrid = () => {
  if (grid.value) {
    addWidgetContent.value += 1; // 增加计数
    grid.value.addWidget({
      w: 1,
      h: 6,
      content: `部件${addWidgetContent.value}`
    });
  }
};
const params = {
  Status: "1",
  AllDates: new Date(Date.now()),
  QueryCriteria: "",
  Rows: "10",
  Page: 1,
  Sidx: "1",
  Sord: "1"
};
// 定义响应式变量，记录当前弹窗对应的布局 ID
const selectedLayoutId = ref("");
const chooseMaterial = async layid => {
  selectedLayoutId.value = layid; // 记录当前布局 ID
  const tb = await getMaterial(params);
  materialList.value = tb.data.list.map(item => ({
    ...item,
    selected: false // 强制设置未选中
  }));
  programDialog.value = true;
};

// 声明一个对象保存当前布局数据
const currentLayout = ref([]);
const saveLayout = () => {
  // 构造 GridStack 容器的 ID
  const gridId = `grid-stack-${currentLayoutId.value}`;

  // 从实例缓存中获取当前布局的 GridStack 实例
  const instance = gridInstances.value[gridId];
  if (!instance) {
    console.warn("当前布局未初始化 GridStack 实例，无法保存");
    return;
  }
  // 获取 GridStack 的原始布局数据
  currentLayout.value = instance.save();
};

// // 监听弹窗打开事件
// watch(dialogVisible, async newVal => {
//   // 设置拖放事件
//   const container = document.querySelector(".right-box");
//   if (container) {
//     container.ondragover = e => {
//       e.preventDefault();
//     };
//     container.addEventListener("drop", addGrid);
//   }

//   const deleteGrid = document.querySelector(".delete-grid");
//   if (deleteGrid) {
//     deleteGrid.ondragover = e => e.preventDefault();
//     deleteGrid.addEventListener("drop", e => {
//       console.log(e);
//     });
//   }
// });

// 表单
const lanchFrom = ref({
  name: "",
  // lanchRespect: "",
  lanchtime: "All",
  // allDay: true,
  // beginTime: "",
  // endTime: "",
  putBegin: "",
  putEnd: ""
});

const rules = reactive({
  name: [{ required: true, message: "请输入投放名称", trigger: "blur" }],
  lanchRespect: [
    {
      required: true,
      message: "请选择投放重复状态",
      trigger: "change"
    }
  ]
});

// 点击投放按钮后
const launch = () => {
  launchDialog.value = true;
};
// 设备选择弹窗
const equipmentDialog = ref(false);
const equipmentList = ref([]);
const lanchEquipment = async () => {
  const tb = await getEquipment(params);
  equipmentList.value = tb.data.list;

  const deviceIdsToSelect = props.changeData.MMDeviceList;

  if (deviceIdsToSelect && deviceIdsToSelect.length > 0) {
    console.log(deviceIdsToSelect);
    await Promise.all(deviceIdsToSelect.map(async DeviceID => {
      const item = equipmentList.value.find(i => i.DeviceID === DeviceID.DeviceID);
      if (item) {
        console.log(item);
        item.selected = true;
      }
    }));
  }
  console.log(tb);
  equipmentDialog.value = true;
};
// 选择的设备列表数据
const selectEquiment = ref([]);
const handleSelectEquipment = item => {
  if (item.selected) {
    selectEquiment.value.push(item);
  } else {
    selectEquiment.value = selectEquiment.value.filter(
      i => i.DeviceID !== item.DeviceID
    );
  }
};
const sureSelectEquipment = () => {
  console.log(selectEquiment.value);
  equipmentDialog.value = false;
};

// 确定添加投放
const addPutMark = async () => {
  // 调用保存模版方法获取模版坐标数据
  saveLayout();
  console.log(currentLayout.value);
  console.log(savueSelectFile.value);
  const regionList = currentLayout.value.map((layoutItem, regionIndex) => {
    // 提取布局区域基础信息
    const region = {
      RegionID: regionIndex, // 或使用 layoutItem.widgetIndex 作为 RegionID
      LayoutID: 0, // 假设 LayoutID 固定为 0，或根据业务逻辑动态设置
      RegionName: "",
      StartX: layoutItem.x,
      StartY: layoutItem.y,
      RegionWidth: layoutItem.w,
      RegionHeight: layoutItem.h,
      CreatedBy: "",
      HtmlTemplate: "",
      PlaylistDetails: {
        DetailID: 0,
        PlaylistID: 0,
        ProgramName: "",
        RegionID: regionIndex, // 与 RegionID 一致
        PlaylistDetailXqList: [] // 待填充文件列表
      }
    };

    // 2. 按 layoutId 过滤选中文件，关联到当前区域
    const matchedFiles = savueSelectFile.value.filter(
      file => file.layoutId === layoutItem.id // 匹配 layoutItem.id（如 "two-row-1"）
    );

    // 3. 构建文件详情列表
    matchedFiles.forEach((file, sequence) => {
      region.PlaylistDetails.PlaylistDetailXqList.push({
        DetailXqID: 0, // 固定值或自增
        DetailID: 0, // 固定值
        FileID: file.FileID,
        Sequence: sequence + 1, // 序号从 1 开始
        AdjustedDuration: file.VideoDuration || 10, // 默认时长
        MMFile: {
          FileID: file.FileID,
          FileName: file.FileName,
          FilePath: file.FilePath,
          FormatType: file.FormatType,
          FileSize: file.FileSize, // 注意：原需求示例中误写为 VideoDuration，此处按实际字段映射
          VideoDuration: file.VideoDuration
        }
      });
    });
    return region; // 必须返回对象
  });

  // 最终结果
  const result = {
    RegionList: regionList
  };

  console.log("处理后的数据：", result);
  const resquestBody = {
    Model: {
      CampaignID: changeData.value.CampaignID? changeData.value.CampaignID : 0,
      CampaignName:lanchFrom.value.name,
      PlaylistID: 0,
      DeviceID: 0,
      MMDeviceList: selectEquiment.value,
      MMLayoutTemplateEntity: {
        LayoutID: 0,
        LayoutName: "二分屏",
        LayoutDescription: "将屏从中分开展示节目效果",
        LayoutRows: 500,
        LayoutCols: 1000,
        TemplateGridCount: 2,
        ...result
      },
      LayoutID: 0,
      StartTime: lanchFrom.value.putBegin,
      EndTime: lanchFrom.value.putEnd
    }
  };

  console.log(resquestBody);
  const res = await addPut(resquestBody);
  console.log(res);
  if (res.state === 200) {
    ElMessage({
      type: "success",
      message: "添加成功"
    });
    dialogFormVisible.value = false;
    handleDialogClose();
    launchDialog.value = false;
  } else {
    ElMessage({
      type: "error",
      message: res.msg
    });
  }
};

const emit = defineEmits(['update:show']);
// 弹窗关闭事件，通知父组件
const handleDialogClose =()=>{
  console.log('关闭事件触发'); // 验证日志
  emit('update:show', false);
}
watch(() => props.changeData, (newVal) => {
  if (newVal !== undefined) { // 当弹窗显示且有数据时
    console.log('数据更新：', newVal);
    lanchFrom.value.name = props.changeData.CampaignName;
    lanchForm.value.
    lanchFrom.value.putBegin = props.changeData.StartTime;
    lanchFrom.value.putEnd = props.changeData.EndTime;
    selectEquiment.value = props.changeData.MMDeviceList;
    console.log(props.changeData.MMDeviceList);
  }
});
</script>
<style lang="scss" scoped>
body {
  padding: 0;
  margin: 0;
}

#home {
  display: flex;
  width: 100%;
  height: calc(100vh - 34px - 68px);
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}

.grid-stack {
  /* 根据页面结构调整 */
  height: 78vh;
}

.left-box {
  padding-top: 10px;
  display: inline;
  display: flex;
  flex-direction: column;
  width: 15%;
  border-right: 2px solid #ebebeb;
  background-color: rgb(49, 49, 146);
}
.delete-grid {
  width: 100px;
  height: 100px;
  text-align: center;
  border: 2px solid #a7a7a7;
  margin: 10px auto;
}

.add-grid {
  width: 100px;
  height: 100px;
  border: 2px solid #808080;
  margin: 10px auto;
  text-align: center;
}

.right-box {
  width: 85%;
  background-color: #ebebeb;
}
.aspect-ratio-16-9 {
  width: 40%;
  aspect-ratio: 16/9;
  border: 1px solid gray;
  margin: 0 auto;
}
.aspect-ratio-9-16 {
  width: 13%;
  aspect-ratio: 9/16;
  border: 1px solid gray;
  margin: 0 auto;
}
.p-0 {
  padding: 0 !important; /* 去除内边距 */
}
.m-0 {
  margin: 0 !important; /* 去除外边距 */
}
.grid-stack-item-conten:active {
  background-color: rgb(67, 150, 202);
  color: white;
}
.grid-stack-item-conten:focus {
  background-color: rgb(67, 150, 202);
  color: white;
}
</style>
<style lang="scss">
.grid-stack-item-content {
  background-color: #ffffff !important;
  text-align: center;
  border: 1px solid gray !important;
}

.grid-stack-item-content {
  padding: 0 !important;
  /* 去除内边距 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
}
/* 弹性容器：控制卡片横向排列 */
.material-container {
  display: flex; /* 启用弹性布局 */
  flex-wrap: wrap; /* 允许卡片换行 */
  gap: 16px; /* 卡片间距（可调整） */
  padding: 16px; /* 容器内边距，避免卡片贴边 */
}

/* 卡片样式：控制单个卡片尺寸和弹性比例 */
.material-card {
  flex: 0 0 calc(25% - 16px); /* 一行4个：25%宽度 - 间距 */
  max-width: calc(25% - 16px); /* 适配响应式布局 */
  min-width: 200px; /* 卡片最小宽度，防止过小 */
}

/* 响应式调整：屏幕较小时减少每行卡片数量 */
@media (max-width: 992px) {
  /* 可根据弹窗宽度调整断点 */
  .material-card {
    flex: 0 0 calc(33.33% - 16px); /* 一行3个 */
  }
}

@media (max-width: 768px) {
  .material-card {
    flex: 0 0 calc(50% - 16px); /* 一行2个 */
  }
}
.equMsg {
  color: rgb(88, 88, 88);
}
</style>
