<template>
  <div
    class="system-selector min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100"
  >
    <div class="max-w-4xl w-full mx-auto p-8">
      <!-- 标题区域 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">选择系统</h1>
        <p class="text-lg text-gray-600">请选择您要进入的系统</p>
      </div>

      <!-- 系统选择卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
        <!-- 管理平台 -->
        <div
          class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105 overflow-hidden"
          @click="enterManagementSystem"
        >
          <div class="p-8 text-center">
            <div
              class="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <el-icon class="text-3xl text-white">
                <Setting />
              </el-icon>
            </div>
            <h3 class="text-2xl font-bold text-gray-800 mb-4">管理平台</h3>
            <p class="text-gray-600 mb-6">
              系统管理、用户管理、数据统计等后台管理功能
            </p>
            <div class="flex flex-wrap gap-2 justify-center mb-6">
              <span
                class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                >用户管理</span
              >
              <span
                class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                >数据统计</span
              >
              <span
                class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                >系统配置</span
              >
            </div>
            <el-button type="primary" size="large" class="w-full">
              进入管理平台
            </el-button>
          </div>
        </div>

        <!-- 收银系统 -->
        <div
          class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105 overflow-hidden"
          @click="enterCashierSystem"
        >
          <div class="p-8 text-center">
            <div
              class="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <el-icon class="text-3xl text-white">
                <Monitor />
              </el-icon>
            </div>
            <h3 class="text-2xl font-bold text-gray-800 mb-4">收银系统</h3>
            <p class="text-gray-600 mb-6">
              KTV房间管理、开房结账、消费记录等收银业务功能
            </p>
            <div class="flex flex-wrap gap-2 justify-center mb-6">
              <span
                class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm"
                >房间管理</span
              >
              <span
                class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm"
                >开房结账</span
              >
              <span
                class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm"
                >消费统计</span
              >
            </div>
            <el-button type="success" size="large" class="w-full">
              进入收银系统
            </el-button>
          </div>
        </div>
      </div>

      <!-- 用户信息和退出 -->
      <div class="text-center mt-12">
        <div
          class="inline-flex items-center space-x-4 bg-white rounded-full px-6 py-3 shadow-md"
        >
          <el-avatar :size="32">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="text-gray-700">{{ currentUser.name || "用户" }}</span>
          <el-button
            type="text"
            @click="logout"
            class="text-gray-500 hover:text-red-500"
          >
            退出登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Setting, Monitor, User } from "@element-plus/icons-vue";
import { getToken, removeToken } from "@/utils/auth";

defineOptions({
  name: "SystemSelector"
});

const router = useRouter();

// 当前用户信息
const currentUser = ref({
  name: "管理员",
  roles: ["admin", "cashier"]
});

// 进入管理平台
const enterManagementSystem = () => {
  router.push("/welcome");
};

// 进入收银系统
const enterCashierSystem = () => {
  router.push("/cashier");
};

// 退出登录
const logout = () => {
  ElMessageBox.confirm("确定要退出登录吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    removeToken();
    router.push("/login");
    ElMessage.success("已退出登录");
  });
};

// 检查登录状态
onMounted(() => {
  if (!getToken()) {
    router.push("/login");
  }
});
</script>

<style scoped>
.system-selector {
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(16, 185, 129, 0.1) 0%,
      transparent 50%
    );
}

.grid > div {
  transition: all 0.3s ease;
}

.grid > div:hover {
  transform: translateY(-8px) scale(1.02);
}

.el-button {
  border-radius: 12px;
  font-weight: 600;
}
</style>
