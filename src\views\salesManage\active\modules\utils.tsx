import { ref} from 'vue';
import {
  addDialog,
} from "@/components/ReDialog";
import {
  GetSalesAndCardsInfoRecord,
  GetWXQrCodeRecord,
} from "@/api/activeCoupon/activeCoupon";
// 使用闭包确保只创建一次状态
let instance = null;

export function getData() {
  if (!instance) {
    // 初始化状态
    const chooseCouponValue = ref([])
    const currentId = ref('');
    const currentCardId = ref("")
    const couponTable = ref([]);
    // 从哪个页面打开
    const TypeName= ref("")
    // 二维码地址，用于渲染二维码图片
    const qrCode = ref('');
    const url = ref('');
    //下载二维码
    const downLoadQrCode = () => {
      console.log('下载二维码');
      const a = document.createElement('a');
      a.href = qrCode.value;
      a.download = '二维码';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
    const onDraggableClick = async () => {
      const key = ref("")
      const reqBody = ref({
        TypeName: TypeName.value,
        "Paging.Rows": "10",
        "Paging.Page": "1",
        "Paging.Sord": "1",
        "Paging.Sidx": "1",
        "Paging.Records": "1",
        SalesId: "",
        CardSheetId:"",
      });
      if (TypeName.value === 'Sales') {
        reqBody.value.SalesId = currentId.value;
      } else if (TypeName.value === 'CardSheet') {
        reqBody.value.CardSheetId = currentCardId.value; // 假设 cardId 也是用 currentId.value
      }
      const res = await GetSalesAndCardsInfoRecord(reqBody.value);
      if (res.data) {
        key.value = res.data.list[0].t;
        url.value =TypeName.value === "Sales"? `pages/CombinationPurchase/pm01/index?scene=${key.value}--0---`:`pages/card/pickUpCoupons/index?scene=t-${key.value}`;
        const result = await GetWXQrCodeRecord({ Path: url.value });
        qrCode.value = URL.createObjectURL(result);
        console.log(result);
        addDialog({
          title: "当前活动二维码",
          draggable: true,
          contentRenderer: () =>
            <div> <img src={qrCode.value} style="margin:20px auto;width:300px;height:300px;display:flex;align-item:center;justify-content: center;"></img>
              <p style="text-align:center;font-size:15px;">活动地址:{url.value}</p>
              <el-button style="margin:20px auto;display:flex;align-item:center;justify-content: center;" onClick={downLoadQrCode}>下载二维码</el-button></div>
         
        });
      }
    }
    instance = {
      chooseCouponValue,
      currentId,
      couponTable,
      qrCode,
      currentCardId,
      onDraggableClick,
      TypeName
    };
  }
  return instance;
}
