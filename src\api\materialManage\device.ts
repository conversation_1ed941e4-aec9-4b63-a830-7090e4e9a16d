import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
import { baseUrlApi } from "../util";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//设备管理接口
//查询全部设备信息的接口
export const getAsyncDevice = (params) => {
  return http.request<any>("get", "/api/MMDevice/GetAll",{params});
};
//新增设备信息的接口
export const AddAsyncDevice = (data) => {
  return http.request<any>("post", "/api/MMDevice/Add",{data});
};
//修改设备信息的接口
export const UpdateAsyncDevice = (data) => {
  return http.request<any>("put", "/api/MMDevice/Update",{data});
};
//删除设备信息的接口
export const DeleteAsyncDevice = (data) => {
  return http.request<any>("delete", "/api/MMDevice/Delete",{data});
};
//根据ID查询设备所绑定的投放节目
export const GetByIdAsyncDevice = (params) => {
  return http.request<any>("get", "/api/MMDevice/GetById",{params});
};
//修改设备绑定的投放节目
export const UpdateLaunchCampaignAdCampaign = (data) => {
  return http.request<any>("put", "/api/MMDevice/UpdateLaunchCampaign",{data});
};

