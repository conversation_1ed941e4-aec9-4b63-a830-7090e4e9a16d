.grid-stack-item-content {
  background-color: #ffffff !important;
  text-align: center;
  border: 1px solid gray !important;
}

.grid-stack-item-content {
  padding: 0 !important;
  /* 去除内边距 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
}
/* 弹性容器：控制卡片横向排列 */
.material-container {
  display: flex; /* 启用弹性布局 */
  flex-wrap: wrap; /* 允许卡片换行 */
  gap: 16px; /* 卡片间距（可调整） */
  padding: 16px; /* 容器内边距，避免卡片贴边 */
}

/* 卡片样式：控制单个卡片尺寸和弹性比例 */
.material-card {
  flex: 0 0 calc(25% - 16px); /* 一行4个：25%宽度 - 间距 */
  max-width: calc(25% - 16px); /* 适配响应式布局 */
  min-width: 200px; /* 卡片最小宽度，防止过小 */
}

/* 响应式调整：屏幕较小时减少每行卡片数量 */
@media (max-width: 992px) {
  /* 可根据弹窗宽度调整断点 */
  .material-card {
    flex: 0 0 calc(33.33% - 16px); /* 一行3个 */
  }
}

@media (max-width: 768px) {
  .material-card {
    flex: 0 0 calc(50% - 16px); /* 一行2个 */
  }
}
.equMsg {
  color: rgb(88, 88, 88);
}
.contentImg{
  width: 100%;
  height: 100%;
  object-fit: fill
}
body {
  padding: 0;
  margin: 0;
}

#home {
  display: flex;
  width: 100%;
  height: calc(100vh - 34px - 68px);
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}

.grid-stack {
  /* 根据页面结构调整 */
  height: 78vh;
}

.left-box {
  padding-top: 10px;
  display: inline;
  display: flex;
  flex-direction: column;
  width: 15%;
  border-right: 2px solid #ebebeb;
  background-color: rgb(49, 49, 146);
}
.delete-grid {
  width: 100px;
  height: 100px;
  text-align: center;
  border: 2px solid #a7a7a7;
  margin: 10px auto;
}

.add-grid {
  width: 100px;
  height: 100px;
  border: 2px solid #808080;
  margin: 10px auto;
  text-align: center;
}

.right-box {
  width: 85%;
  background-color: #ebebeb;
}
.aspect-ratio-16-9 {
  width: 40%;
  aspect-ratio: 16/9;
  border: 1px solid gray;
  margin: 0 auto;
}
.aspect-ratio-9-16 {
  width: 13%;
  aspect-ratio: 9/16;
  border: 1px solid gray;
  margin: 0 auto;
}
.p-0 {
  padding: 0 !important; /* 去除内边距 */
}
.m-0 {
  margin: 0 !important; /* 去除外边距 */
}
.grid-stack-item-conten:active {
  background-color: rgb(67, 150, 202);
  color: white;
}
.grid-stack-item-conten:focus {
  background-color: rgb(67, 150, 202);
  color: white;
}