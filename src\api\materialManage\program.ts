import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//节目管理接口
//查询全部投放节目的接口
export const getAsyncAdCampaign = (params) => {
  return http.request<any>("get", "/api/MMAdCampaign/GetAll",{params});
};
//新增投放节目的接口
export const AddAsyncAdCampaign = (data) => {
  return http.request<any>("post", "/api/MMAdCampaign/Add",{data});
};
//修改投放节目的接口
export const UpdateAsyncAdCampaign = (data) => {
  return http.request<any>("put", "/api/MMAdCampaign/Update",{data});
};
//删除投放节目的接口
export const DeleteAsyncAdCampaign = (data) => {
  return http.request<any>("delete", "/api/MMAdCampaign/Delete",{data});
};
//根据投放任务ID查询投放节目以及下面关联的布局和节目信息的接口
export const GetDetailsAsyncAdCampaign = (params) => {
  return http.request<any>("get", "/api/MMAdCampaign/GetDetails",{params});
};
//根据ID查询投放节目和投放设备
export const GetByIdAsyncAdCampaign = (params) => {
  return http.request<any>("get", "/api/MMAdCampaign/GetById",{params});
};
//修改投放节目绑定的投放设备
export const UpdateLaunchDeviceAsyncAdCampaign = (data) => {
  return http.request<any>("put", "/api/MMAdCampaign/UpdateLaunchDevice",{data});
};
export const GetDeviceById = (params) => {
  return http.request<any>("get", "/api/MMAdCampaign/GetDetails",{params});
};
