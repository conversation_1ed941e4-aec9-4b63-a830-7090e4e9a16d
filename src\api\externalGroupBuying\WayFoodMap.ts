import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
import { baseUrlApi } from "../util";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//团购管理接口
//查询全部团购信息的接口
export const getAsyncWayFoodMap = (params) => {
  return http.request<any>("get", "/api/WayFoodMap/GetAll",{params});
};
//新增团购信息的接口
export const AddAsyncWayFoodMap = (data) => {
  return http.request<any>("post", "/api/WayFoodMap/Add",{data});
};
//批量导入团购信息的接口
export const ImportWayFoodMap = (data) => {
  return http.request<any>("post", "/api/WayFoodMap/Import",{data},{
      headers: {
        // 自动设置 multipart/form-data boundary
        'Content-Type': 'multipart/form-data'  
      }
  });
};
//修改团购信息的接口
export const UpdateAsyncWayFood = (data) => {
  return http.request<any>("put", "/api/WayFoodMap/Update",{data});
};
//删除团购信息的接口
export const DeleteAsyncWayFood = (data) => {
  return http.request<any>("delete", "/api/WayFoodMap/Delete",{data});
};

//查询全部团购门店信息的接口
export const getAsyncWayStoreMap = (params) => {
  return http.request<any>("get", "/api/WayFoodMap/GetStoreAll",{params});
};


