import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//文件夹管理接口
//查询所有文件夹信息的接口
export const getFolderAsync= (params) => {
  return http.request<any>("get", "/api/MMFolder/GetAll",{params});
};
//新增文件接口
export const AddFolderAsyncFile = (data) => {
  return http.request<any>("post", "/api/MMFolder/Add", { data });
}
//根据ID查询文件
export const GetByIdAsyncFolder = (params) => {
  return http.request<any>("get", "/api/MMFolder/GetById",{params});
};
// //修改素材的接口（暂无需调用）
// export const UpdateAsyncFile = (data) => {
//   return http.request<any>("put", "/api/MMFile/Update",{data});
// };
// //删除素材的接口
// export const DeleteAsyncFile = (data) => {
//   return http.request<any>("delete", "/api/MMFile/Delete",{data});
// };
