<template>
  <div class="class-management">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索节目..."
        clearable
        class="search-input"
        @clear="fetchAdCampaign"
        @change="fetchAdCampaign"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="fetchAdCampaign">
        <el-icon><Search /></el-icon>
        查询投放节目
      </el-button>
      <el-button type="success" @click="showProgramAddPage">
        <el-icon><Plus /></el-icon>
        新增投放节目
      </el-button>
    </div>

    <!-- 投放节目卡片列表 -->
    <el-row
      v-if="modelList.length"
      v-loading="loading"
      :gutter="20"
      class="class-list"
    >
      <el-col
        v-for="item in modelList"
        :key="item.CampaignID"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <el-card class="class-card">
          <template #header>
            <div class="class-header">
              <span class="title-name">{{ item.CampaignName }}</span>
            </div>
          </template>

          <div class="card-content">
            <div class="info-item">
              <el-icon><Key /></el-icon>
              <span class="label">投放任务编码：</span>
              <span class="value">{{ item.CampaignID }}</span>
            </div>
          </div>

          <div class="card-actions">
            <el-button plain @click="showModelDatils(item.CampaignID)"
              >投放设备</el-button
            >
            <el-tooltip content="编辑投放任务" placement="top">
              <el-button type="primary" plain @click="changeModelDetils(item)"
                >编辑</el-button
              >
            </el-tooltip>

            <el-popconfirm
              title="确认删除该投放任务?"
              @confirm="deleteAdCampaign(item.CampaignID)"
            >
              <template #reference>
                <el-button type="danger" plain>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else description="未查询到记录" />
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.Rows"
      tabs
      :total="pagination.Records"
      :page-sizes="[5, 10, 15]"
      layout="total, sizes, prev, pager, next, jumper"
      class="pagination"
      @size-change="handlePaginationChange"
      @current-change="handlePaginationChange"
    />
    <!-- 投放任务对话框 -->
    <el-dialog v-model="dialogVisible" width="700px">
      <el-descriptions title="投放任务信息" :column="2" border>
        <el-descriptions-item label="编码">{{
          DeviceShowData.CampaignID
        }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{
          DeviceShowData.CampaignName
        }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{
          formatDate(DeviceShowData.StartTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{
          formatDate(DeviceShowData.EndTime)
        }}</el-descriptions-item>
      </el-descriptions>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="设备列表" name="devicelist">
          <el-button type="primary" @click="showDeviceModelDatils"
            >新增设备</el-button
          >
          <el-table
            :data="DeviceShowData.MMDeviceList"
            style="width: 100%; height: 200px"
          >
            <el-table-column prop="DeviceID" label="设备编码" width="180" />
            <el-table-column prop="DeviceName" label="设备名称" width="180" />
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="removeDevicebtn(scope.$index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="SubmitBdDevice">确认</el-button>
      </template>
    </el-dialog>
    <!-- 选择设备的信息框 -->
    <el-dialog v-model="DevicedialogVisible" title="设备信息" width="500px">
      <div class="mt-4">
        <el-button type="primary" @click="revertValueBtn">返回</el-button>
        <el-input
          v-model="searchDeviceKeyword"
          style="width: 200px"
          placeholder="请输入设备名称"
          class="input-with-select ml-2"
        >
          <template #append>
            <el-button @click="QueryDeviceBtn" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="设备列表" name="devicelist">
          <el-table
            :data="DeviceShowData2"
            row-key="DeviceID"
            style="width: 100%; height: 500px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60" />
            <el-table-column prop="DeviceID" label="设备编码" width="180" />
            <el-table-column prop="DeviceName" label="设备名称" width="180" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- 设备列表分页 -->
      <el-pagination
        v-model:current-page="Devicepagination.current"
        v-model:page-size="Devicepagination.Rows"
        size="small"
        background
        layout="prev, pager, next"
        :total="Devicepagination.Records"
        @size-change="handleDevicePaginationChange"
        @current-change="handleDevicePaginationChange"
        class="mt-4 ml-14"
      />
    </el-dialog>
    <AddProgramList
      @refreshParentData="fetchAdCampaign"
      v-model:show="showAdd"
      v-model:changeData="changeData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import AddProgramList from "./modules/add/addprogramList.vue";
import {
  Search,
  Plus,
  Edit,
  Delete,
  Key,
  Clock,
  Cpu
} from "@element-plus/icons-vue";
import axios from "axios";
import { ElMessage } from "element-plus";
import {
  getAsyncAdCampaign,
  DeleteAsyncAdCampaign,
  GetByIdAsyncAdCampaign,
  UpdateLaunchDeviceAsyncAdCampaign,
  GetDeviceById
} from "@/api/materialManage/program.ts";
import { getAsyncDevice } from "@/api/materialManage/device";
// 设备数据相关
const loading = ref(false);
const modelList = ref([]);
const searchKeyword = ref("");
const searchDeviceKeyword = ref("");
const DeviceShowData = ref({});
const DeviceShowData2 = ref({});
const activeName = ref("devicelist");
//分页
var pagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});
//设备分页
var Devicepagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});

// 对话框相关
const dialogVisible = ref(false);
const DevicedialogVisible = ref(false);
const dialogType = ref("add");
const selectedRows = ref([]); // 存储选中行数据

// 新增或修改弹窗相关
const showAdd = ref(false);
const changeData = ref({});

// 复选框变化时更新选中数据
const handleSelectionChange = val => {
  selectedRows.value = val;
};
//移除投放设备
const removeDevicebtn = index => {
  DeviceShowData.value.MMDeviceList.splice(index, 1);
};

//保存投放设备
const SubmitBdDevice = async () => {
  const UpdateLaunchDevice = {
    CampaignID: DeviceShowData.value.CampaignID,
    CampaignName: DeviceShowData.value.CampaignName,
    mMPlaybackDevice: DeviceShowData.value.MMDeviceList.map(item => ({
      CampaignID: DeviceShowData.value.CampaignID,
      DeviceID: item.DeviceID
    }))
  };
  var response = await UpdateLaunchDeviceAsyncAdCampaign(UpdateLaunchDevice);
  ElMessage.success(response.message);
  dialogVisible.value = false;
};
//点击返回按钮返回设备数据
const revertValueBtn = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.warning("未选择信息返回！");
    return;
  }
  // 深拷贝选中的数据
  const copiedData = JSON.parse(JSON.stringify(selectedRows.value));

  // 合并到表格中（避免重复）
  const existingIds = DeviceShowData.value.MMDeviceList.map(
    item => item.DeviceID
  );
  const newData = copiedData.filter(
    item => !existingIds.includes(item.DeviceID)
  );

  DeviceShowData.value.MMDeviceList = [
    ...DeviceShowData.value.MMDeviceList,
    ...newData
  ];
  DevicedialogVisible.value = false;
  selectedRows.value = [];
};
//点击按钮查询设备
const QueryDeviceBtn = () => {
  showDeviceModelDatils();
};
// 获取投放任务列表
const fetchAdCampaign = async () => {
  loading.value = true;
  try {
    //编写参数对象
    const params = {
      Status: "1",
      AllDates: new Date(Date.now()),
      QueryCriteria: searchKeyword.value,
      Rows: pagination.Rows,
      Page: pagination.current,
      Sidx: "CampaignID",
      Sord: "desc"
    };
    // 调用设备接口方法getAsyncDevice，并将参数传入
    const response = await getAsyncAdCampaign(params);
    modelList.value = response.data.list;
    pagination = response.data.paging;
  } finally {
    loading.value = false;
  }
};

// 显示对话框
const showDialog = (type, device) => {
  dialogType.value = type;
  if (type === "edit") {
    ElMessage.success("修改成功");
    //Object.assign(formData, device);
  } else {
    ElMessage.success("新增成功");
  }
  dialogVisible.value = true;
};
// 点击新增投放节目打开对应弹窗
const showProgramAddPage = () => {
  showAdd.value = true;
  changeData.value = {};
};
onMounted(() => {
  fetchAdCampaign();
});

// 显示投放任务详情
const showModelDatils = async id => {
  const deleteAdCampaign = {
    CampaignID: id
  };
  var response = await GetByIdAsyncAdCampaign(deleteAdCampaign);
  if (response.data != null) {
    DeviceShowData.value = response.data;
    dialogVisible.value = true;
  }
};

// 显示设备详情
const showDeviceModelDatils = async () => {
  //编写参数对象
  const params = {
    Status: "1",
    AllDates: new Date(Date.now()),
    QueryCriteria: searchDeviceKeyword.value,
    Rows: Devicepagination.Rows,
    Page: Devicepagination.current,
    Sidx: "DeviceID",
    Sord: "desc"
  };
  var response = await getAsyncDevice(params);
  DeviceShowData2.value = response.data.list;
  Devicepagination = response.data.paging;
  DevicedialogVisible.value = true;
};

// 删除投放任务
const deleteAdCampaign = async id => {
  const deleteAdCampaign = {
    CampaignID: id
  };
  var response = await DeleteAsyncAdCampaign(deleteAdCampaign);
  await fetchAdCampaign();
  ElMessage.success(response.message);
};

// 分页处理
const handlePaginationChange = () => {
  fetchAdCampaign();
};
// 设备分页处理
const handleDevicePaginationChange = () => {
  showDeviceModelDatils();
};

// 日期格式化
const formatDate = date => {
  return new Date(date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

const changeModelDetils = async item => {
  console.log(item.CampaignID);
  const deleteAdCampaign = {
    CampaignID: item.CampaignID
  };
  const res = await GetDeviceById(deleteAdCampaign);
  changeData.value = res.data;
  showAdd.value = true;
};
// 初始化加载
onMounted(fetchAdCampaign);
</script>

<style scoped lang="scss">
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.class-management {
  padding: 20px;
  background-color: #f5f7fa;
  .operation-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }
  }

  .class-list {
    margin-bottom: 20px;
    height: 85%;
  }

  .class-card {
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .class-header {
      display: flex;
      align-items: center;
      gap: 10px;

      .title-name {
        font-weight: bold;
        font-size: 16px;
      }
    }

    .card-content {
      padding: 10px 0;

      .info-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
          color: #666;
        }

        .label {
          color: #666;
          margin-right: 5px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;

    .search-input {
      width: 100% !important;
    }
  }
}
</style>
