import { http} from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
export const getMaterial = (params?: object) => {
  return http.request<any>("get", "/api/MMFile/GetAll", { params },
  );
};

export const getEquipment = (params?: object) => {
  return http.request<any>("get", "/api/MMDevice/GetAll", { params },
  );
};
export const addPut = (data?: object) => {
  return http.request<any>("post", "/api/MMAdCampaign/Add", { data },
  );
};
export const changePut = (data?: object) => {
  return http.request<any>("put", "/api/MMAdCampaign/Update", { data },
  );
};

