<template>
  <div>
      <!-- 新建投放任务弹窗 -->
    <el-dialog
    v-model="dialogFormVisible"
    width="100%"
    style="height:100%; margin:0px; padding-top:40px;"

    @close="handleDialogClose"
    >
      <!-- <div style="width: 100%; height:100%;"> -->
        <div
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <el-button type="primary" @click="launch">投放</el-button>
        </div>
        <el-tabs
        v-model="chooseTag"
          tab-position="left"
          style="height: 100%"
          class="demo-tabs"
          @update:modelValue="changeChoose"
        >
          <el-tab-pane label="选择画布" name="draw">
            <el-tabs
   
              v-model="chooseCanvas"
              tab-position="top"
              style="height: 100%"
              class="demo-tabs"
              @update:modelValue="handleTabChange"
            >
              <el-tab-pane name="1" label="横向" class="p-0 m-0">
                <div class="aspect-ratio-16-9">
                  <div v-if="savueSelectFile.length > 0" style="width: 100%; height: 100%">
                    <img
                          v-if="savueSelectFile[0].FormatType.includes('image')"
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"
                        />
                        <video
                          v-if="savueSelectFile[0].FormatType.includes('mp4')"
                          controls
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"
                        ></video>
                        <audio
                          v-if="savueSelectFile[0].FormatType.includes('audio')"
                          controls
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"
                        ></audio>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="2" label="纵向">
                <div class="aspect-ratio-9-16">
                  <div v-if="savueSelectFile.length > 0" style="width: 100%; height: 100%">
                    <img
                          v-if="savueSelectFile[0].FormatType.includes('image')"
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"
                        />
                        <video
                          v-if="savueSelectFile[0].FormatType.includes('mp4')"
                          controls
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"

                        ></video>
                        <audio
                          v-if="savueSelectFile[0].FormatType.includes('audio')"
                          controls
                          :src="baseUrl+savueSelectFile[0].FilePath"
                          class="contentImg"

                        ></audio>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="选择布局" name="chooseLayout">
            <el-tabs
              class="demo-tabs"
              v-model="currentLayoutId"
              @update:modelValue="handleLayoutChange"
            >
              <!-- 动态生成布局选项卡 -->
              <el-tab-pane
                v-for="layoutItem in layoutDemo"
                :key="layoutItem.id"
                :label="layoutItem.name"
                :name="layoutItem.id"
              >
              <!-- <div
                  :class="`aspect-ratio-${chooseCanvas === '1' ? '16-9' : '9-16'}`"
                >
                  <div
                    :id="`grid-stack-${layoutItem.id}`"
                    class="grid-stack"
                    style="
                      border: 2px dashed lightgrey;
                      width: 100%;
                      height: 100%;
                    "
                  ></div>
                </div> -->
                 <!-- 动态生成布局容器，确保每个布局有独立的 DOM -->
  <div
    :class="`aspect-ratio-${chooseCanvas === '1' ? '16-9' : '9-16'}`"
  >
    <div
      :id="`grid-stack-${layoutItem.id}`" 
      class="grid-stack"
      style="border: 2px dashed lightgrey; width: 100%; height: 100%;"
    ></div>
  </div>
      <!-- </div> -->
      <!-- </div> -->
                <div
                  style="
                    width: 100%;
                    height: 330px;
                    border: 1px solid gray;
                    margin-top: 15px;
                    padding: 10px;
                  "
                >
                  <el-scrollbar height="310px">
                    <p v-if="savueSelectFile.length == 0">您已选择的素材会展示在这里</p>
                    <el-row :gutter="10">
                      <el-col v-for="item in savueSelectFile" :span="2" style="height:200px; margin-right:40px; width: 100%;">
                        <!-- 显示模块名称 -->
    <p class="module-name">{{ getContentById(item.layoutId) }}</p>
                        <img
                          v-if="item.FormatType.includes('image')"
                          :src="baseUrl+item.FilePath"
                          style="width: 100%; height: 100%"
                        />
                        <video
                          v-if="item.FormatType.includes('mp4')"
                          controls
                          :src="baseUrl+item.FilePath"
                          style="width: 100%; height: 100%"
                        ></video>
                        <audio
                          v-if="item.FormatType.includes('audio')"
                          controls
                          :src="baseUrl+item.FilePath"
                          style="width: 100%; height: 100%"
                        ></audio>
                        <el-button style="width: 50px; margin: 0px 15px;" @click="delectSelectFile(item)">删除</el-button>
                      </el-col>
                    </el-row>
                  </el-scrollbar>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
        </el-tabs>
      <!-- </div> -->
      <!-- </div> -->
    </el-dialog>
    <!-- 投放按钮弹窗 -->
    <el-dialog v-model="launchDialog" title="投放信息" width="600" draggable>
      <!-- 添加弹性容器包裹卡片 -->
      <el-form
        :model="lanchFrom"
        label-width="auto"
        style="max-width: 500px"
        :rules="dynamicRules"
        ref="formRef"
      >
        <el-form-item label="投放名称" prop="name">
          <el-input v-model="lanchFrom.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="全天播放">
          <el-switch v-model="lanchFrom.allDay" />
        </el-form-item>
        <el-form-item label = "播放时间" v-if="lanchFrom.allDay===true">
          <el-form-item>
            <el-col :span="11">
              <el-form-item prop="putBegin">
                <el-time-picker
                v-model="lanchFrom.startTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                placeholder="选择开始播放时间"
                style="width: 100%"
              />
            </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">-</span>
            </el-col>
            <el-col :span="11">
              <el-form-item  prop="putEnd">
              <el-time-picker
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="lanchFrom.endTime"
                placeholder="选择结束播放时间"
                style="width: 100%"
              />
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-form-item>
        <el-form-item
          label="投放时间"
          placeholder="请选择投放时间"
          prop="lanchtime"
          v-if="lanchFrom.allDay===false"
        >
          <!-- <el-radio-group v-model="lanchFrom.lanchtime">
            <el-radio value="Stay">长期投放</el-radio>
            <el-radio value="All">定时投放</el-radio>
          </el-radio-group> -->
          <el-form-item>
            <el-col :span="11">
              <el-form-item prop="putBegin">
                <el-date-picker
                v-model="lanchFrom.putBegin"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                placeholder="选择开始投放时间"
                style="width: 100%"
              />
            </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">-</span>
            </el-col>
            <el-col :span="11">
              <el-form-item  prop="putEnd">
              <el-date-picker
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="lanchFrom.putEnd"
                placeholder="选择结束投放时间"
                style="width: 100%"
              />
              </el-form-item>
            </el-col>
          </el-form-item>
          <!-- <el-select v-model="lanchFrom.lanchRespect">
            <el-option label="每天重复" value="shanghai" />
            <el-option label="每周重复" value="beijing" />
            <el-option label="每年重复" value="beijing" />
          </el-select> -->
        </el-form-item>
       
        <!-- <el-form-item label="播放时间" v-if="lanchFrom.allDay === false">
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.beginTime"
              type="date"
              placeholder="选择开始播放时间"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.endTime"
              placeholder="选择结束播放时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item> -->
        <el-form-item label="投放设备">
          <el-button @click="lanchEquipment">选择投放设备</el-button>
        </el-form-item>
        <el-form-item label="已选择设备" v-if="selectEquiment.length > 0">
          <span
            v-if="selectEquiment.length > 0"
            v-for="item in selectEquiment"
            style="margin: 0 10px; color: gray"
          >
            {{ item.DeviceName }}
          </span>
        </el-form-item>
      </el-form>
      <!-- 设备选择弹窗 -->
      <EquipmentDialog v-model:equipmentDialog="equipmentDialog"/>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="launchDialog = false">取消</el-button>
          <el-button type="primary" @click="addPutMark"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 素材选择弹窗 -->
    <el-dialog
      v-model="programDialog"
      title="素材选择"
      width="1000"
      draggable
      style="max-height: 800px"
    >
      <el-scrollbar height="400px">
        <!-- 添加弹性容器包裹卡片 -->
        <div class="material-container">
          <el-card
            v-for="item in materialList"
            :key="item.id"
            class="material-card"
            style="max-width: 300px"
             v-loading="programLoading"
          >
            <!-- 添加多选框 -->
            <div class="card-header">
              <el-checkbox
                v-model="item.selected"
                :true-label="item.id"
                @change="handleSelectionChange(item)"
              ></el-checkbox>
            </div>
            <!-- 图片区域 -->
            <div class="card-image">
              <img
                v-if="item.FormatType.includes('image')"
                :src="baseUrl+item.FilePath"
                style="width: 100%"
              />
              <video
                v-if="item.FormatType.includes('mp4')"
                controls
                :src="baseUrl+item.FilePath"
                style="width: 100%"
              ></video>
              <audio
                v-if="item.FormatType.includes('audio')"
                controls
                :src="baseUrl+item.FilePath"
                style="width: 100%"
              ></audio>
            </div>
          </el-card>
        </div>
      </el-scrollbar>
      <el-pagination
    size="small"
    background
    layout="prev, pager, next"
    :total="pagination.Records"
     v-model:current-page="pagination.current"
    class="mt-4"
     @current-change="materialCurrentChange"
  />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSelectionChange">取消</el-button>
          <el-button type="primary" @click="getchooseList"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { Delete, Download, Plus, ZoomIn } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import "./addprogramList.css"
import { addPut ,changePut} from "@/api/programList";
import { computed, watch, ref, nextTick, reactive, toRefs,onUnmounted } from "vue";
import { GridStack } from "gridstack";
import { baseUrlApi } from "@/api/util.ts";
import "gridstack/dist/gridstack.min.css";
import EquipmentDialog from './equipmentDialog.vue'
import { getData } from "./getData";
import axios from "axios";
const { materialList, getMateriaList,getEquimentList,equipmentList, selectEquiment , pagination, materialCurrentChange } = getData();
// 接收父组件的值

onUnmounted(() => {
  // 清理所有GridStack实例
  Object.values(gridInstances.value).forEach(instance => {
    if (instance && instance.destroy) {
      instance.destroy();
    }
  });
  gridInstances.value = {};
});
const baseUrl ="https://hdcore.tang-hui.com.cn/"
const props = defineProps({
  show: Boolean,
  changeData: Object,
});

// 解构后在模板中可以直接使用 `show` 和 `changeData`
const { show, changeData } = toRefs(props);

// tag切换部分
const currentLayoutId = ref("0"); // 当前选中的主布局（二分屏/三分屏）
const chooseTag = ref("draw") //选择模板或者画布tag的绑定值
const chooseCanvas = ref("1"); //选择画布
const handleTabChange = newTab => {
  // console.log("当前选中的画布:", chooseCanvas.value);
  // console.log("画布方向已切换:", newTab);
  chooseCanvas.value = newTab;
};
const changeChoose =async (newValue) => {
  // console.log(newValue);
  if (newValue == "chooseLayout") {
    // 当标签值是选择布局的时候调用初始化布局方法
    handleLayoutChange(currentLayoutId.value);
  }
}; 


// 布局部分
const layoutDemo = ref([
  {
    id: "0",
    name: "默认屏幕",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 6, content: "素材1", id: 11}
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 6, content: "素材1", id: 21}
        ]
      }
    ]
  },
  {
    id: "1",
    name: "二分屏",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 6, h: 6, content: "左边内容", id: 121},
          { x: 6, y: 0, w: 6, h: 6, content: "右边内容", id: 122}
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 3, content: "上面内容", id: 221},
          { x: 0, y: 3, w: 12, h: 3, content: "下面内容", id: 222 }
        ]
      }
    ]
  },
  {
    id: "2",
    name: "三分屏",
    layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 4, h: 6, content: "左边内容", id:131 },
          { x: 4, y: 0, w: 4, h: 6, content: "中间内容", id: 132 },
          { x: 8, y: 0, w: 4, h: 6, content: "右边内容", id: 133 }
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          {
            x: 0,
            y: 0,
            w: 12,
            h: 2,
            content: "上面内容",
            id: 231
          },
          {
            x: 0,
            y: 2,
            w: 12,
            h: 2,
            content: "中间内容",
            id: 232
          }, // 修正坐标
          { x: 0, y: 4, w: 12, h: 2, content: "下面内容", id:233} // 修正坐标
        ]
      }
    ]
  }
]);
const grid = ref(null); // 统一使用这个ref管理实例
const gridInstances = ref({}); // 存储各布局的 GridStack 实例
// 初始化布局方法
// const initLayout = async () => {
//   if (currentLayoutId.value && layoutDemo.value.length > 0) {
//     await handleLayoutChange(currentLayoutId.value);
//     await nextTick();
//   }
// };

const initLayout = async () => {
  try {
    // ... 初始化代码 ...
    if (currentLayoutId.value && layoutDemo.value.length > 0) {
      await handleLayoutChange(currentLayoutId.value);
    }
  } catch (error) {
    console.error('布局初始化失败:', error);
    ElMessage.error('布局加载失败，请重试');
  }
};

// 动态生成布局
const handleLayoutChange = async newLayoutId => {
  savueSelectFile.value = []; // 清空素材列表
   // 清理旧实例
//   const oldGridId = `grid-stack-${currentLayoutId.value}`;
//   console.log("旧的gridId:", oldGridId);
// if (gridInstances.value[oldGridId]) {
//   gridInstances.value[oldGridId].destroy();
//   const oldGridRef = document.getElementById(oldGridId);
//   if (oldGridRef) {
//      oldGridRef.innerHTML = ''; // 清空内容而非删除容器 // 清空子节点而非删除容器
//   }
//   delete gridInstances.value[oldGridId];
// }
await nextTick()
  // console.log("当前选中的布局ID:", newLayoutId);
  // 初始化新布局的 GridStack
  const layoutItem = layoutDemo.value.find(item => item.id === newLayoutId);
  // console.log("当前选中的布局:", layoutItem);
  await nextTick(); // 等待 DOM 更新
  // 直接获取当前方向的布局（横屏或竖屏）
  const currentOrientation = chooseCanvas.value === '1' ? '横屏' : '竖屏';
  const targetOrientation = layoutItem.layout.find(
    orient => orient.name === currentOrientation
  );
  // console.log("当前LAYOUT:", targetOrientation);
  if (!targetOrientation) return; // 确保方向存在
  // layoutItem.layout.forEach((orientation) => {
    const gridId = `grid-stack-${newLayoutId}`;
    const gridRef = document.getElementById(gridId);
    if (!gridRef) {
  console.error("容器` ${gridId}`不存在");
  return;
}
    if (gridRef) {
    // **新增：清空旧的 DOM 节点**
    gridRef.innerHTML = ''; // 清除残留的 widget 节点
      console.log("高度:", gridRef.clientHeight);
      const options = {
        margin: 0,
        cellHeight: gridRef.clientHeight / 6,
        column: 12,
        maxRow: 6,
        disableDrag: true,
        disableResize: true,
        animate: false
      };
      // 定义点击事件处理函数
      GridStack.renderCB = (el, node) => {
        // el.innerHTML = node.content || "点击选择素材";
        // 设置初始内容
        const widgetMaterial = savueSelectFile.value.find(m => m.widgetId === node.widgetIndex);
        console.log("当前选择的素材:", widgetMaterial);
            if (widgetMaterial) {
              // 如果有已选择的素材，显示素材预览
              if (widgetMaterial.FormatType.includes('image')) {
                el.innerHTML = `<img src="${baseUrl+widgetMaterial.FilePath}" alt="素材预览" class="contentImg">`;
              } else if (widgetMaterial.FormatType.includes('mp4')) {
                el.innerHTML = `<video controls src="${baseUrl+widgetMaterial.FilePath}" class="contentImg"></video>`;
              } else if (widgetMaterial.FormatType.includes('audio')) {
                el.innerHTML = `<audio controls src="${baseUrl+widgetMaterial.FilePath}" class="w-full h-full"></audio>`;
              } else {
                el.innerHTML = widgetMaterial.Name || '未知类型素材';
              }
            } else {
              // 没有选择素材，显示默认提示
              el.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">
                <i class="fa fa-plus-circle mr-2"></i>点击选择素材
              </div>`;
            }
        el.dataset.widgetIndex = node.widgetIndex; // 添加数据属性
        // 监听鼠标悬停方法
        el.addEventListener("click", () => {
          console.log("当前选中的布局:", node.widgetIndex);
          chooseMaterial(node.widgetIndex);
        });
        el.classList.add('transition-all', 'duration-300');
        el.addEventListener("mouseover", () => {
          el.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');
            });
            el.addEventListener('mouseleave', () => {
              el.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
            });
      };
      const instance = GridStack.init(options, gridRef);
      targetOrientation.layoutList.forEach(widgetConfig => {
        console.log("当前选中的布局:", widgetConfig);
        instance.addWidget({
          ...widgetConfig,
          content: "点击选择素材",
          widgetIndex: widgetConfig.id
        });
      });
      //       gridRef.addEventListener("click", (e) => {
      //         console.log("点击事件:", e.target.closest('[data-index]')?.dataset.index);
      //         const widgetIndex = e.target.closest('[data-index]')?.dataset.index;
      //         console.log("当前选中的布局:", widgetIndex);
      //         if (widgetIndex !== undefined) {
      //           handleWidgetClick(Number(widgetIndex));
      //   }
      // });
      // widget1.addEventListener("click", () => handleWidgetClick(0));
      // 存储实例以便后续操作
      gridInstances.value[gridId] = instance;
      console.log("当前布局:", gridInstances.value[gridId]);
    }
  // });
  // 更新当前选中的布局ID
  currentLayoutId.value = newLayoutId;
};

// 监听父组件传递的展示
const dialogFormVisible = ref(show);
const launchDialog = ref(false); //点击投放按钮后的弹窗
// 素材部分
const programDialog = ref(false); //素材弹窗
// 保存素材文件
const savueSelectFile =ref([]);
// 更新素材信息
const updateWidgetContent = (widgetIndex, material) => {
      const gridId = `grid-stack-${currentLayoutId.value}`;
      console.log(gridId)
      const grid = gridInstances.value[gridId];
      console.log(gridInstances.value[gridId])
      
      if (grid) {
        // 找到对应的widget
        const widget = grid.engine.nodes.find(node => node.widgetIndex === widgetIndex);
        console.log(material)
        if (widget) {
          // 更新widget内容
          const el = document.querySelector(`[data-widget-index="${widgetIndex}"]`);
          console.log(el);
          if (el) {
            if(material !== null && material.length > 0){
              for (var i = 0; i < material.length; i++){
              if (material[0].FormatType.includes('image')) {
              el.innerHTML = `<img src="${baseUrl+material[0].FilePath}" alt="${material[0].Name}" class="contentImg transition-opacity duration-500">`;
            } else if (material[0].FormatType.includes('mp4')) {
              el.innerHTML = `<video controls src="${baseUrl+material[0].FilePath}" class="contentImg"></video>`;
            } else if (material[0].FormatType.includes('audio')) {
              el.innerHTML = `<audio controls src="${baseUrl+material[0].FilePath}" class="w-full h-full"></audio>`;
            } else {
              el.innerHTML = material.Name || '未知类型素材';
            }
            }
            }else{
              el.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">
                <i class="fa fa-plus-circle mr-2"></i>点击选择素材
              </div>`;
              }    
}        
        }
      }
    };
// const autoGridStackInit = () => {
//   const autoCridStack1 = document.querySelector("#grid-stack-auto-row");
//   const autoGridStack2 = document.querySelector("#grid-stack-auto-coloumn");
//   // 确保DOM已经渲染
//   if (!autoCridStack1 && !autoGridStack2) {
//     console.error("GridStack容器未找到");
//     return;
//   }

//   const renderLayout = (gridStackRef, layout) => {
//     const containerHeight = gridStackRef.clientHeight;
//     console.log(containerHeight);
//     const options = {
//       dragOut: true,
//       margin: 0,
//       allowHtml: true,
//       cellHeight: containerHeight / 6,
//       column: 12,
//       maxRow: 6,
//       maxHeight: containerHeight,
//       minHeight: 6,
//       animate: false,
//       acceptWidgets: true, //接受从其他网格或外部拖动的小部件
//       dragIn: ".add-grid", //可以从外部拖动的类
//       dragInOptions: {
//         revert: "invalid",
//         scroll: false, //当元素被拖动到网格的底部或顶部时启用或禁用滚动
//         appendTo: "body", //添加到body中
//         helper: "clone" //放置时的辅助函数=>克隆
//       }, //可以从外部拖动类的配置
//       removable: "#trash", //在拖动到网格外时删除小部件的类
//       removeTimeout: 100 //在拖动到网格外时删除小部件之前的时间 100毫
//     };
//     GridStack.renderCB = (el, node) => {
//       el.innerHTML = node.content || "";
//     };
//     grid.value = GridStack.init(options, gridStackRef);

//     // 添加拖拽事件监听
//     const draggableItems = document.querySelectorAll(".add-grid");
//     draggableItems.forEach(item => {
//       item.addEventListener("dragstart", e => {
//         // 设置拖拽数据，可以包含小部件的配置信息
//         e.dataTransfer.setData(
//           "text/plain",
//           JSON.stringify({
//             content: `<div class="widget-content">自定义小部件</div>`,
//             w: 4, // 默认宽度
//             h: 2 // 默认高度
//           })
//         );
//       });
//     });

//     // 处理从外部拖入的事件
//     grid.value.on("added", (e, items) => {
//       console.log("添加了新小部件:", items);
//       // 这里可以添加自定义逻辑，例如设置小部件的样式或内容
//     });
//     if (grid.value) {
//       if (layout === "autoRow") {
//         grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       } else if (layout === "autoColumn") {
//         grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       }
//     }
//   };
//   if (autoCridStack1) {
//     renderLayout(autoCridStack1, "autoRow");
//   }
//   if (autoGridStack2) {
//     renderLayout(autoGridStack2, "autoColumn");
//   }
// };


// 弹窗选中信息
const selectdItem = ref([]);
// 选中素材方法
const handleSelectionChange = item => {
  if (item.selected) {
    selectdItem.value.push(item);
  } else {
    selectdItem.value = selectdItem.value.filter(i => i.FileID !== item.FileID);
  }
};
//关闭弹窗后执行方法
const closeSelectionChange = item => {
  item.selected = false;
  programDialog.value = false;
};
// 确定选中文件后执行方法
const getchooseList = () => {
  // 检查是否有有效的布局 ID（避免未选择布局时出错）
  if (!selectedLayoutId.value) {
    console.warn("未选择布局，无法绑定 ID");
    ElMessage({
      type: "error",
      message: "布局绑定失败，请重新绑定"
    });
    return;
  }
  // 过滤出选中的文件，并绑定对应的 layoutId
  const selectedFiles = materialList.value
    .filter(item => item.selected) // 筛选已选中的文件
    .map(file => ({
      ...file,
      layoutId: selectedLayoutId.value // 使用当前弹窗的布局 ID
    }));
    console.log(selectdItem.value)
      // 更新GridStack中的widget内容
  updateWidgetContent(selectedLayoutId.value, selectedFiles); 
  // 更新全局选中列表（可选：合并或替换原有数据）
  savueSelectFile.value = [...savueSelectFile.value,...selectedFiles]; // 直接赋值为当前布局的选中文件
  // 或合并到现有列表：selectdItem.value = [...selectdItem.value, ...selectedFiles];
  console.log("绑定后的文件数据：", savueSelectFile.value);
  programDialog.value = false;
};

// 素材展示之删除按钮方法
const delectSelectFile = (item) => {
  console.log(item);
  const index = savueSelectFile.value.findIndex(i => i.FileID === item.FileID && i.layoutId === item.layoutId);
  if (index!== -1) {
    // 获取要删除的item的widgetIndex
    const widgetIndex = savueSelectFile.value[index].layoutId;
    savueSelectFile.value = savueSelectFile.value.filter(i => i.FileID!== item.FileID || i.layoutId !== item.layoutId);
    console.log(savueSelectFile.value);
    console.log("删除后的文件数据：", savueSelectFile.value);
    updateWidgetContent(widgetIndex, []);
  }
};
// 添加组件技术  --- 暂未使用
const addWidgetContent = ref(0);
//增加自定义组件---暂未使用
const addGrid = () => {
  if (grid.value) {
    addWidgetContent.value += 1; // 增加计数
    grid.value.addWidget({
      w: 1,
      h: 6,
      content: `部件${addWidgetContent.value}`
    });
  }
};

// 定义响应式变量，记录当前弹窗对应的布局 ID
const selectedLayoutId = ref("");

const programLoading = ref(false);
// 打开选择素材弹窗
const chooseMaterial = async layid => {
  programLoading.value = true;
  selectedLayoutId.value = layid; // 记录当前布局 ID
  // 修改默认打开页面显示第一个
  pagination.value.current = 1;
  // 调用getData.tsx文件中的获取数据方法
  getMateriaList();
  await nextTick();
  programLoading.value = false
  programDialog.value = true;
};

// 声明一个对象保存当前布局数据 全局使用
const currentLayout = ref([]);
// 保存模板后模板信息的转换方法 用于拿到横纵高宽
const saveLayout = () => {
  // 构造 GridStack 容器的 ID
  console.log(currentLayoutId.value)
  const gridId = `grid-stack-${currentLayoutId.value}`;

  // 从实例缓存中获取当前布局的 GridStack 实例
  const instance = gridInstances.value[gridId];
  if (!instance) {
    console.warn("当前布局未初始化 GridStack 实例，无法保存");
    return;
  }
  // 获取 GridStack 的原始布局数据
  currentLayout.value = instance.save();
  console.log(currentLayout.value);
};
// // 监听弹窗打开事件  设置拖放----暂未使用
// watch(dialogVisible, async newVal => {
//   // 设置拖放事件
//   const container = document.querySelector(".right-box");
//   if (container) {
//     container.ondragover = e => {
//       e.preventDefault();
//     };
//     container.addEventListener("drop", addGrid);
//   }

//   const deleteGrid = document.querySelector(".delete-grid");
//   if (deleteGrid) {
//     deleteGrid.ondragover = e => e.preventDefault();
//     deleteGrid.addEventListener("drop", e => {
//       console.log(e);
//     });
//   }
// });

// 表单
const lanchFrom = ref({
  name: "",
  allDay: true,
  putBegin: "",
  putEnd: "",
  startTime: "",
  endTime: ""
});
//绑定form
const formRef = ref(null)
// 表单验证规则
const dynamicRules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: "请输入投放名称", trigger: "blur" }]
  };
  
  if (lanchFrom.allDay === false) {
    return {
      ...baseRules,
      putBegin: [{ required: true, message: "请选择开始投放时间", trigger: "change" }],
      putEnd: [{ required: true, message: "请选择结束投放时间", trigger: "change" }]
    };
  } else {
    return {
      ...baseRules,
      startTime: [{ required: true, message: "请选择开始播放时间", trigger: "change" }],
      endTime: [{ required: true, message: "请选择结束播放时间", trigger: "change" }]
    };
  }
});

// 点击投放按钮后
const launch = () => {
  launchDialog.value = true;
};

// 设备部分
// 设备选择弹窗
const equipmentDialog = ref(false);
// const equipmentList = ref([]);
const lanchEquipment = async () => {
  pagination.value.current = 1;
  await getEquimentList();
  const deviceIdsToSelect = props.changeData.MMDeviceList;
  if (deviceIdsToSelect && deviceIdsToSelect.length > 0) {
    console.log(deviceIdsToSelect);
    await Promise.all(deviceIdsToSelect.map(async DeviceID => {
      const item = equipmentList.value.find(i => i.DeviceID === DeviceID.DeviceID);
      if (item) {
        console.log(item);
        item.selected = true;
      }
    }));
  }
  equipmentDialog.value = true;
};
// // 处理选择设备  选择的设备数据
// const selectEquiment = ref([]);
// const handleSelectEquipment = item => {
//   if (item.selected) {
//     selectEquiment.value.push(item);
//   } else {
//     selectEquiment.value = selectEquiment.value.filter(
//       i => i.DeviceID !== item.DeviceID
//     );
//   }
// };
// 点击选择设备按钮
// const sureSelectEquipment = () => {
//   console.log(selectEquiment.value);
//   equipmentDialog.value = false;
// };

// 确定添加投放
const addPutMark = async () => {
  console.log(lanchFrom.value);
  formRef.value.validate(async valid => {
    if (valid && selectEquiment.value.length !==0 && savueSelectFile.value.length !==0) {
      // 验证通过，执行提交逻辑
      // 调用保存模版方法获取模版坐标数据
  saveLayout();
  console.log(currentLayout.value);
  console.log(savueSelectFile.value);
  const regionList = currentLayout.value.map((layoutItem, regionIndex) => {
    console.log(regionIndex)
    // 提取布局区域基础信息
    const region = {
      RegionID: 0, // 或使用 layoutItem.widgetIndex 作为 RegionID
      LayoutID: 0, // 假设 LayoutID 固定为 0，或根据业务逻辑动态设置
      RegionName:"",
      StartX: layoutItem.x,
      StartY: layoutItem.y,
      RegionWidth: layoutItem.w,
      RegionHeight: layoutItem.h,
      CreatedBy: "",
      HtmlTemplate: "",
      PlaylistDetails: {
        DetailID: 0,
        PlaylistID: 0,
        ProgramName:"",
        RegionID: regionIndex, // 与 RegionID 一致
        PlaylistDetailXqList: [] // 待填充文件列表
      }
    };
    // 2. 按 layoutId 过滤选中文件，关联到当前区域
    const matchedFiles = savueSelectFile.value.filter(
      file => file.layoutId === layoutItem.id // 匹配 layoutItem.id（如 "two-row-1"）
    );
    console.log(matchedFiles);
    // 3. 构建文件详情列表
    matchedFiles.forEach((file, sequence) => {
      region.PlaylistDetails.PlaylistDetailXqList.push({
        DetailXqID: 0, // 固定值或自增
        DetailID: 0, // 固定值
        FileID: file.FileID,
        Sequence: sequence + 1, // 序号从 1 开始
        AdjustedDuration: file.VideoDuration || 10, // 默认时长
        MMFile: {
          FileID: file.FileID,
          FileName: file.FileName,
          FilePath: file.FilePath,
          FormatType: file.FormatType,
          FileSize: file.FileSize, // 注意：原需求示例中误写为 VideoDuration，此处按实际字段映射
          VideoDuration: file.VideoDuration
        }
      });
    });
    return region; // 必须返回对象
  });
      // **新增：查询布局名称**
      const findLayoutName = () => {
    // 从当前选中的布局ID获取对应的layoutDemo项
    const currentLayoutItem = layoutDemo.value.find(item => item.id === currentLayoutId.value);
    if (!currentLayoutItem) return "未知布局";
    
    
    // 根据当前方向（横屏/竖屏）找到对应的layout配置
    const orientationKey = chooseCanvas.value === "1" ? "横屏" : "竖屏";
    const orientationLayout = currentLayoutItem.layout.find(l => l.name === orientationKey);
    if (!orientationLayout) return currentLayoutItem.name;
    
    // 拼接方向和布局名称（如 "横屏二分屏"）
    return `${orientationLayout.name}${currentLayoutItem.name}`;
  };
  // 获取布局名称
  const layoutName = findLayoutName();
  console.log("当前布局名称:", layoutName); // 输出示例："横屏二分屏"
  // 最终结果
  const result = {
    RegionList: regionList
  };

      console.log("处理后的数据：", result);
      selectEquiment.value.forEach(item => {
  item.DeviceModel = item.DeviceName; // 将 DeviceModel 设置为 DeviceName 的值
      });
      if (JSON.stringify(changeData.value) !== '{}') {
        selectEquiment.value = selectEquiment.value.map(item => ({
  DeviceID: item.DeviceID || null,
          DeviceName: item.DeviceName || "",
  DeviceModel:item.DeviceName 
}));
      }
  const resquestBody = {
    Model: {
      CampaignID: changeData.value.CampaignID? changeData.value.CampaignID : 0,
      CampaignName:lanchFrom.value.name,
      PlaylistID: changeData.value.PlaylistID? changeData.value.PlaylistID : 0,
      DeviceID: 0,
      MMDeviceList: selectEquiment.value,
      MMLayoutTemplateEntity: {
        LayoutID: changeData.value.LayoutID? changeData.value.LayoutID : 0,
        LayoutName: layoutName,
        LayoutDescription: "将屏从中分开展示节目效果",
        LayoutRows: 500,
        LayoutCols: 1000,
        TemplateGridCount: 2,
        ...result
      },
      LayoutID: changeData.value.LayoutID? changeData.value.LayoutID : 0,
      StartTime:lanchFrom.value.allDay?formatDateForDotnet(lanchFrom.value.startTime):formatDateForDotnet(lanchFrom.value.putBegin),
      EndTime: lanchFrom.value.allDay?formatDateForDotnet(lanchFrom.value.endTime):formatDateForDotnet(lanchFrom.value.putEnd),
      IsItDaily:lanchFrom.value.allDay
    }
  };

      console.log(resquestBody);
      console.log(lanchFrom.value.putBegin)
  //     // 发起添加请求
      if (JSON.stringify(changeData.value) === '{}') {
        const res = await addPut(resquestBody);
        console.log(res);
        ElMessage({
      type: "success",
      message: "添加成功"
    });
    dialogFormVisible.value = false;
    handleDialogClose();
    launchDialog.value = false;
    // **关键步骤**：触发父组件更新事件
    emit('refreshParentData'); // 传递参数可选
      } else if (JSON.stringify(changeData.value) !== '{}') {
        const res = await changePut(resquestBody);
          console.log(res.data); // 建议访问响应体数据
        dialogFormVisible.value = false;
        ElMessage({
      type: "success",
      message: "修改成功"
    });
    handleDialogClose();
    launchDialog.value = false;
    // **关键步骤**：触发父组件更新事件
    emit('refreshParentData'); // 传递参数可选
  }
    //执行验证失败
    } else {
      // 验证失败
      if (selectEquiment.value.length == 0) {
        ElMessage({
      type: "error",
      message: "您未选择投放设备"
    });
      } else if (
        savueSelectFile.value.length == 0
      ) {
        ElMessage({
      type: "error",
      message: "您未选择模板播放素材"
    });
      }
     
      console.log('表单验证失败');
      return false;
    }
  });
};

// 传递给父组件
const emit = defineEmits(['update:show', 'refreshParentData']);
// 弹窗关闭事件，通知父组件
const handleDialogClose =()=>{
  console.log('关闭事件触发'); // 验证日志
  emit('update:show', false);
}


// 清空页面数据绑定方法
const resetData = () => {
  // 清空表单数据（lanchFrom）
  lanchFrom.value = {
    name: "",
    lanchtime: "Stay",
    putBegin: "",
    putEnd: "",
    allDay: true,
  };

  // 清空选中设备
  selectEquiment.value = [];

  // 清空选中素材
  savueSelectFile.value = [];

  // 重置布局相关状态
  currentLayoutId.value = "0"; // 默认布局
  chooseCanvas.value = "1"; // 默认横屏
  currentLayout.value = []; // 清空布局数据

  // 关闭所有弹窗
  launchDialog.value = false;
  equipmentDialog.value = false;
  programDialog.value = false;
};

// 监听父组件传值   用于修改
watch(
  [() => props.show, () => props.changeData],
  ([newShow, newVal]) => {
    console.log (newShow,newVal)
    if (newShow == true && JSON.stringify(newVal) === '{}') {  
        // 当 show 为 true 且 changeData 为空时，重置数据
      resetData();
      return;
    } else if (newShow &&  newVal) {
      console.log('数据更新：', newVal);
      lanchFrom.value.name = props.changeData.CampaignName;
      if (props.changeData.IsItDaily) {
        lanchFrom.value.startTime = props.changeData.StartTime;
        lanchFrom.value.endTime = props.changeData.EndTime;
      } else {
        lanchFrom.value.putBegin = props.changeData.StartTime;
        lanchFrom.value.putEnd = props.changeData.EndTime;
    }
    lanchFrom.value.allDay = props.changeData.IsItDaily;
    selectEquiment.value = props.changeData.MMDeviceList;
    console.log(props.changeData.MMDeviceList);
    // 根据LayoutName设置默认值
      const layoutName = props.changeData.MMLayoutTemplateEntity.LayoutName;
      chooseTag.value = "chooseLayout"
      console.log(chooseTag.value);
    if (layoutName.includes('横屏')) {
      chooseCanvas.value = '1';
    } else if (layoutName.includes('竖屏')) {
      chooseCanvas.value = '2';
    }

    if (layoutName.includes('二分屏')) {
      currentLayoutId.value = '1';
    } else if (layoutName.includes('三分屏')) {
      currentLayoutId.value = '2';
    } else if (layoutName.includes('默认屏幕')) {
      currentLayoutId.value = '0';
    }
      initLayout();
       // 解析RegionList中的素材
      const regionList = newVal.LayoutRegions;
      console.log(layoutDemo.value);
      // 匹配文件对应的layoutId 用坐标匹配
      const allLayoutItems = layoutDemo.value.flatMap(layoutType => 
  layoutType.layout.flatMap(orientation => 
    orientation.layoutList
  )
);
      regionList.forEach((region) => {
        const matchedLayoutItem = allLayoutItems.find(
          item => isRegionMatch(item, region)
        );
      const layoutId = matchedLayoutItem ? matchedLayoutItem.id : region.RegionID;
      const materials = region.PlaylistDetails.PlaylistDetailXqList.map((xq) => {
        return {
          FileID: xq.FileID,
          FilePath: xq.MMFile.FilePath,
          FormatType: xq.MMFile.FormatType,
          FileName: xq.MMFile.FileName,
          layoutId: layoutId // 关联布局ID
        };
      });
      savueSelectFile.value.push(...materials);
        console.log(savueSelectFile.value);
      });
     
    }
  });

// 素材id对应
// 预构建id到content的映射表
const idToContentMap = computed(() => {
  const map = new Map();
  layoutDemo.value.forEach(layoutType => {
    layoutType.layout.forEach(orientation => {
      orientation.layoutList.forEach(item => {
        map.set(item.id, item.content);
      });
    });
  });
  return map;
});

// 修改模板中的方法调用
const getContentById = (id) => {
  return idToContentMap.value.get(id) || `未知模块(${id})`;
};


// 工具方法，拼接当前时间
const getFormattedDateTime = (time) => {
  // 获取当前日期（年/月/日）
  // const currentDate = new Date();
  // const year = currentDate.getFullYear();
  // const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，转为 01-12
  // const day = String(currentDate.getDate()).padStart(2, '0'); // 日期补零

  // // 拼接日期和时间参数（假设 lanchFrom.value.startTime 存在）
  // const dateTimeStr = `${year}-${month}-${day} ${time}`; // 格式："YYYY-MM-DD HH:mm"
  // console.log(dateTimeStr);
  // 调用格式化方法
  const formattedDate = formatDateForDotnet(time);
  console.log(formattedDate);
  return formattedDate;
};

// 工具方法：将日期转换为 .NET 友好的 ISO 8601 格式
const formatDateForDotnet = (localTimeStr) => {
  if (!localTimeStr) return null;
  
  // 解析本地时间字符串为 Date 对象（根据本地时区）
  const date = new Date(localTimeStr);
  
  // 获取本地时区偏移量（分钟）
  const timeZoneOffset = date.getTimezoneOffset(); // UTC 比本地时间早的分钟数，例如 UTC+8 时为 -480
  const sign = timeZoneOffset > 0 ? '-' : '+';
  const hours = Math.floor(Math.abs(timeZoneOffset) / 60);
  const minutes = Math.abs(timeZoneOffset) % 60;
  
  // 构造带时区偏移的 ISO 字符串（如 2025-05-19T20:58:53+08:00）
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours24 = String(date.getHours()).padStart(2, '0');
  const minutes2 = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0'); // 可选，根据需求保留
  
  return `${year}-${month}-${day}T${hours24}:${minutes2}:${seconds}.${milliseconds}${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};
// 匹配坐标的方法
const isRegionMatch = (regionA, regionB) => {
  return (
    Math.abs(regionA.x - regionB.StartX) <= 1 &&
    Math.abs(regionA.y - regionB.StartY) <= 1 &&
    Math.abs(regionA.w - regionB.RegionWidth) <= 1 &&
    Math.abs(regionA.h - regionB.RegionHeight) <= 1
  );
};
</script>
<style lang="scss" scoped>
body {
  padding: 0;
  margin: 0;
}

#home {
  display: flex;
  width: 100%;
  height: calc(100vh - 34px - 68px);
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}

.grid-stack {
  /* 根据页面结构调整 */
  height: 78vh;
}

.left-box {
  padding-top: 10px;
  display: inline;
  display: flex;
  flex-direction: column;
  width: 15%;
  border-right: 2px solid #ebebeb;
  background-color: rgb(49, 49, 146);
}
.delete-grid {
  width: 100px;
  height: 100px;
  text-align: center;
  border: 2px solid #a7a7a7;
  margin: 10px auto;
}

.add-grid {
  width: 100px;
  height: 100px;
  border: 2px solid #808080;
  margin: 10px auto;
  text-align: center;
}

.right-box {
  width: 85%;
  background-color: #ebebeb;
}
.aspect-ratio-16-9 {
  width: 40%;
  aspect-ratio: 16/9;
  border: 1px solid gray;
  margin: 0 auto;
}
.aspect-ratio-9-16 {
  width: 13%;
  aspect-ratio: 9/16;
  border: 1px solid gray;
  margin: 0 auto;
}
.p-0 {
  padding: 0 !important; /* 去除内边距 */
}
.m-0 {
  margin: 0 !important; /* 去除外边距 */
}
.grid-stack-item-conten:active {
  background-color: rgb(67, 150, 202);
  color: white;
}
.grid-stack-item-conten:focus {
  background-color: rgb(67, 150, 202);
  color: white;
}
</style>
<style lang="scss">
.grid-stack-item-content {
  background-color: #ffffff !important;
  text-align: center;
  border: 1px solid gray !important;
}

.grid-stack-item-content {
  padding: 0 !important;
  /* 去除内边距 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
}
/* 弹性容器：控制卡片横向排列 */
.material-container {
  display: flex; /* 启用弹性布局 */
  flex-wrap: wrap; /* 允许卡片换行 */
  gap: 16px; /* 卡片间距（可调整） */
  padding: 16px; /* 容器内边距，避免卡片贴边 */
}

/* 卡片样式：控制单个卡片尺寸和弹性比例 */
.material-card {
  flex: 0 0 calc(25% - 16px); /* 一行4个：25%宽度 - 间距 */
  max-width: calc(25% - 16px); /* 适配响应式布局 */
  min-width: 200px; /* 卡片最小宽度，防止过小 */
}

/* 响应式调整：屏幕较小时减少每行卡片数量 */
@media (max-width: 992px) {
  /* 可根据弹窗宽度调整断点 */
  .material-card {
    flex: 0 0 calc(33.33% - 16px); /* 一行3个 */
  }
}

@media (max-width: 768px) {
  .material-card {
    flex: 0 0 calc(50% - 16px); /* 一行2个 */
  }
}
.equMsg {
  color: rgb(88, 88, 88);
}
.contentImg{
  width: 100%;
  height: 100%;
  object-fit: fill
}
</style>
