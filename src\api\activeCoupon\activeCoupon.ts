import { http } from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
//团购管理接口
//查询全部团购信息的接口
export const GetSalesAndCardsInfoRecord = (params: object) => {
  return http.request<any>("get", "MarketingActivity/GetSalesAndCardsInfoRecord",{params});
};
// 新增活修改卡券信息
export const EditorSalesAndCardsInfoRecord = (data: object) => {
  
  return http.request<any>("post", "/MarketingActivity/EditorSalesAndCardsInfoRecord",{data},{ headers: {
    // 自动设置 multipart/form-data boundary
    'Content-Type': 'multipart/form-data'  
  }});
};
// 删除卡券信息
export const DeleteSalesAndCardsInfoRecord = (data: object) => {
  return http.request<any>("post", "/MarketingActivity/DeleteSalesAndCardsInfoRecord",{data});
};
// 查询卡券信息
export const GetCardSheetListRecord = (params: object) => {
  return http.request<any>("get", "/MarketingActivity/GetCardSheetListRecord",{params});
};

// 获取活动二维码
export const GetWXQrCodeRecord = (params: object) => {
  return http.request<any>("get", "/MarketingActivity/GetWXQrCodeRecord", {
    'responseType': 'blob'
 },{params});
};

