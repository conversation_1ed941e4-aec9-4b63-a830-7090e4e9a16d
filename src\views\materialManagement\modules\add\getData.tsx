import { getMaterial, getEquipment } from "@/api/programList";
import { ref, reactive } from 'vue';
// 使用闭包确保只创建一次状态
let instance = null;

export function getData() {
  if (!instance) {
    // 初始化状态
    const params = ref({
      Status: "1",
      AllDates: new Date(Date.now()),
      QueryCriteria: "",
      Rows: "10",
      Page: 1,
      Sidx: "1",
      Sord: "1"
    })
    const pagination = ref({
      Records: 0,
      Total: 0,
      current: 1,
    })

    const materialList = ref([]);

    // 获取素材列表的函数
    function getMateriaList(): Promise<void> {
      params.value.Page = pagination.value.current;
      return getMaterial(params.value).then((res) => { // 返回Promise
        pagination.value.Records = res.data.paging.Records
        pagination.value.Total = res.data.paging.Total
        materialList.value = res.data.list.map(item => ({
          ...item,
          selected: false // 强制设置未选中
        }));
        // console.log("数据已更新:", materialList.value)
      })
    }

    function materialCurrentChange() {
      params.value.Page = pagination.value.current
      getMateriaList()
    }
    const equipmentList = ref([]);
    // 处理选择设备  选择的设备数据
    const selectEquiment = ref([]);
    // 获取设备列表
    function getEquimentList(): Promise<void> {
      params.value.Page = pagination.value.current;
      return getEquipment(params.value).then((res) => { // 返回Promise
        console.log("设备列表数据:", res.data.list)
        pagination.value.Records = res.data.paging.Records
        pagination.value.Total = res.data.paging.Total
        equipmentList.value = res.data.list
      })
    }

    // 创建实例
    instance = {
      params,
      pagination,
      materialList,
      getMateriaList,
      equipmentList,
      selectEquiment,
      getEquimentList,
      materialCurrentChange
    };
  }

  return instance;
}
