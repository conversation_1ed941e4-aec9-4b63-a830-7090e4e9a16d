<script setup>
import {
  onMounted,
  watch,
  ref,
  toRefs,
  nextTick,
  shallowRef,
  onBeforeUnmount,
  reactive
} from "vue";
import Sortable from "sortablejs";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import CouponDialog from "./chooseCoupons.vue";
import { getData } from "./modules/utils";
import "@wangeditor/editor/dist/css/style.css"; // 引入 css
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import dayjs from "dayjs";
import {
  GetSalesAndCardsInfoRecord,
  EditorSalesAndCardsInfoRecord,
  GetCardSheetListRecord
} from "@/api/activeCoupon/activeCoupon";
const { chooseCouponValue, currentId, couponTable } = getData();
console.log(chooseCouponValue.value);
// 接收父组件的值
const props = defineProps({
  editVisible: Boolean,
  state: String
});
const { editVisible, state } = toRefs(props);
const emit = defineEmits(["update:editVisible"]);
// 定义数据初始状态
const initialState = {
  bodyList: {
    title: "堂会活动",
    key: "",
    isShowTime: false,
    actionEvent: {},
    config: {},
    trigger: {},
    isShowItemCou: true,
    isShowTitleCou: false,
    isTest: false,
    state: 1,
    msg: "立即抢购",
    begintime: "",
    endtime: "",
    couTag: "",
    bana: "",
    cardList: []
  },
  couponForm: {
    GrouponName: "",
    GrouponPrice: "",
    GoodsPrice: "",
    qty: 1,
    Explain: ""
  },
  form: {
    title: "",
    begintime: "",
    endtime: "",
    value2: []
  },
  imageUrl: "",
  fileList: [],
  selectedFile: null
};

// 关闭弹窗时触发的事件
const visibleClose = () => {
  emit("update:editVisible", false);
};
// 重置数据事件
const resetData = () => {
  bodyList.value = { ...initialState.bodyList };
  couponForm.value = { ...initialState.couponForm };
  form.value = { ...initialState.form };
  imageUrl.value = "";
  fileList.value = [];
  selectedFile.value = null;
};
const title = ref("编辑活动");
const saleDialogLoad = ref(false);
// 监听父组件传来的值，如果为true则打开弹窗
watch(
  () => props.editVisible,
  async newVal => {
    if (newVal) {
      if (state.value == "edit" || state.value == "see") {
        resetData();
        saleDialogLoad.value = true;
        bodyList.value = {
          cardList: []
        };
        const reqBody = {
          TypeName: "Sales",
          "Paging.Rows": "10",
          "Paging.Page": "1",
          "Paging.Sord": "1",
          "Paging.Sidx": "1",
          "Paging.Records": "1",
          SalesId: currentId.value
        };
        const res = await GetSalesAndCardsInfoRecord(reqBody);
        bodyList.value = res.data.list[0];
        imageUrl.value = res.data.list[0].DownloadUrl;
        console.log(imageUrl.value);
        console.log(bodyList.value);
        console.log(res);
        form.value.value2 = [bodyList.value.begintime, bodyList.value.endtime];
        title.value = "编辑活动";
        saleDialogLoad.value = false;
      } else if (state.value == "add") {
        title.value = "新增活动";
        console.log("打开了添加活动窗口");
        title.value = "新增活动";
        Editor;
        chooseCouponValue.value = [];
        resetData();
        form.value.value2 = [bodyList.value.begintime, bodyList.value.endtime];
      }
    }
  }
);
watch(
  () => chooseCouponValue.value,
  newVal => {
    console.log(newVal);
    if (!newVal || newVal.length === 0) return;

    // 获取当前bodyList中已有的卡券ID集合
    const existingIds = new Set(
      bodyList.value.cardList.map(item => item.GrouponKey)
    );

    // 筛选出新卡券（不在现有列表中的）
    const newItems = newVal.filter(item => !existingIds.has(item.GrouponKey));

    if (newItems.length > 0) {
      // 合并新卡券到现有列表
      bodyList.value.cardList = [...bodyList.value.cardList, ...newItems];
      console.log(`添加了${newItems.length}个新卡券`, newItems);
    }
  },
  { deep: true } // 深度监听，确保能检测到数组变化
);
// 监听弹窗传递的state
watch(
  () => props.state,
  async newVal => {
    if (newVal == "edit") {
    } else if (newVal == "add") {
    }
  }
);

const num = ref(0);
const bodyList = ref({});
const columns = ref([
  {
    label: "卡券名称",
    prop: "GrouponName",
    slot: "GrouponName"
  },
  {
    label: "售价",
    prop: "GrouponPrice",
    width: "80px"
  },
  {
    label: "门市价",
    prop: "GoodsPrice",
    width: "80px"
  },
  {
    label: "操作",
    slot: "operation",
    fixed: "right",
    width: "100px"
  }
]);

const form = ref({
  title: "",
  begintime: "",
  endtime: "",
  value2: []
});
// 生成带前缀的日期字符串
const generateDateString = () => {
  return dayjs().format("YYYYMMDDHHmmss");
};

// 生成自增ID的函数
const createIdGenerator = prefix => {
  let counter = 1;
  return () => {
    const id = `${prefix}${counter.toString().padStart(3, "0")}`;
    counter++;
    return id;
  };
};
const formRef = ref(null);
// 保存按钮
const fromRules = async () => {
  console.log(form.value.value2);
  try {
    await formRef.value.validate();
    console.log("submit!");
    if (form.value.value2[0] === "" || form.value.value2[1] === "") {
      ElMessage({
        message: "请选择时间",
        type: "warning"
      });
      return false;
    }
    if (!imageUrl.value) {
      ElMessage({
        message: "请选择图片",
        type: "warning"
      });
      return false;
    }
    if (bodyList.value.cardList.length === 0) {
      ElMessage({
        message: "请选择卡券",
        type: "warning"
      });
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
};
const onSubmit = async () => {
  const isValid = await fromRules();
  // 验证不通过则直接返回
  if (!isValid) return;

  try {
    console.log("submit!");
    saleDialogLoad.value = true;
    const [startTime, endTime] = form.value.value2;
    const dateStr = generateDateString();
    const salePrefix = `SALEKBOSS${dateStr}`;

    // 创建G_id生成器
    const generateGId = createIdGenerator(salePrefix);

    // 格式化为字符串（可选）
    const formattedData = {
      starttime: formatDate(startTime, "YYYY-MM-DD HH:mm:ss"),
      endtime: formatDate(endTime, "YYYY-MM-DD HH:mm:ss")
    };
    if (state.value === "add") {
      bodyList.value = {
        ...bodyList.value, // 保留原有属性
        t: `SALES${dateStr}`,
        begintime: formattedData.starttime,
        endtime: formattedData.endtime,
        key: `KBOSS${dateStr}`,
        couTag: `${salePrefix}000`,
        CreationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        Type: "Sales",
        IsDelete: false,
        cardList: bodyList.value.cardList.map(item => ({
          G_id: generateGId(), // 生成新的 G_id
          qty: item.qty == 0 ? 1 : item.qty, // 设置默认数量
          GrouponName: item.GrouponName,
          GrouponPrice: item.GrouponPrice,
          GoodsPrice: item.GoodsPrice,
          Explain: item.Explain ? item.Explain : "无",
          GrouponKey: item.GrouponKey ? item.GrouponKey : "09808989890"
          // 其他需要保留的字段...
        }))
      };
      // const SalesJsonData = ref({
      //   ...bodyList.value
      // });
      const jsonString = JSON.stringify(bodyList.value);
      const req = {
        File: selectedFile.value,
        Data: jsonString
      };
      const res = await EditorSalesAndCardsInfoRecord(req);
      console.log(res);
      if (res.state === 200) {
        ElMessage({
          message: "保存成功",
          type: "success"
        });
        bodyList.value.SalesId = res.data[0].DataId;
        bodyList.value._id = res.data[0].IdList;
        console.log(bodyList.value);
        saleDialogLoad.value = false;
      }
    } else {
      console.log("bodyList.value", bodyList.value);
      bodyList.value = {
        ...bodyList.value, // 保留原有属性
        t: bodyList.value.t ? bodyList.value.t : `SALES${dateStr}`,
        begintime: formattedData.starttime,
        endtime: formattedData.endtime,
        key: bodyList.value.key ? bodyList.value.key : `KBOSS${dateStr}`,
        couTag: bodyList.value.couTag
          ? bodyList.value.couTag
          : `${salePrefix}000`,
        CreationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        Type: "Sales",
        IsDelete: false,
        cardList: bodyList.value.cardList.map(item => ({
          G_id: generateGId(), // 生成新的 G_id
          qty: item.qty == 0 ? 1 : item.qty,
          GrouponName: item.GrouponName,
          GrouponPrice: item.GrouponPrice,
          GoodsPrice: item.GoodsPrice,
          Explain: item.Explain ? item.Explain : "无",
          GrouponKey: item.GrouponKey ? item.GrouponKey : "09808989890"
          // 其他需要保留的字段...
        }))
      };
      const jsonString = JSON.stringify(bodyList.value);
      const req = {
        File: selectedFile.value ? selectedFile.value : bodyList.value.bana,
        Data: jsonString
      };
      const res = await EditorSalesAndCardsInfoRecord(req);
      console.log(res);
      if (res.state === 200) {
        ElMessage({
          message: "保存成功",
          type: "success"
        });
        saleDialogLoad.value = false;
      }
    }
    // 验证通过，执行提交逻辑
  } catch (error) {
    console.error("表单验证失败:", error);
    ElMessage.warning("请检查表单填写是否正确");
  }
};
// 日期格式化函数（可使用dayjs或原生）
const formatDate = (date, format) => {
  return dayjs(date).format(format);
};
// 卡券弹窗
const couponDialog = ref(false);
// 卡券弹窗状态
const couponState = ref("sale");
//选择卡券信息
const chooseCoupons = () => {
  couponDialog.value = true;
  couponTable.vale = bodyList.value.cardList;
};
// --同步卡券细则弹窗
const syncDialog = ref(false);

const batchSync = () => {
  syncDialog.value = true;
  // console.log("批量同步卡券细则");
  bodyList.value.cardList.forEach(item => {
    item.Explain = cleanEditorContent(item.Explain); // 格式化HTML内容
    item.newExplain = ""; // 初始化为空字符串
  });
  initEditorsRefs();
};
// 修改卡券信息
const dialogFormVisible = ref(false);
const formLabelWidth = "80px";
const couponForm = ref({
  GrouponName: "",
  GrouponPrice: "",
  GoodsPrice: "",
  qty: 1,
  Explain: ""
});
// 查找索引
const currentEditIndex = ref(-1); // 当前编辑项的索引
const editCoupons = row => {
  dialogFormVisible.value = true;
  console.log(row);
  // 找到当前编辑项在数组中的索引
  currentEditIndex.value = bodyList.value.cardList.findIndex(
    item => item === row // 或者根据唯一标识比较，如 item.id === row.id
  );

  couponForm.value = {
    ...row,
    Explain: formatHtml(row.Explain),
    qty: row.qty || 1
  };
  // 每次编辑时改变key，强制重新创建编辑器
  editorKey.value++;
  titleKey.value++;
  console.log(couponForm.value);
  // 输出富文本框的key
};

// 监听数据变化
watch(
  () => couponForm.value.Explain,
  newVal => {
    if (editorRef.value) {
      try {
        const cleanedHtml = cleanEditorContent(newVal);
        editorRef.value.setHtml(cleanedHtml);
      } catch (e) {
        console.error("更新编辑器内容失败:", e);
      }
    }
  }
);

// 格式化html代码
function formatHtml(html) {
  // 1. 移除Office特有标签
  html = html
    .replace(/<\/?o:[^>]*>/g, "")
    .replace(/<w:[^>]*>/g, "")
    .replace(/<\/w:[^>]*>/g, "")
    .replace(/<span\s+lang="[^"]*"[^>]*>/g, "<span>");

  // 2. 清理空标签
  html = html.replace(/<(\w+)[^>]*>\s*<\/\1>/g, "");

  // 3. 保留必要样式
  html = html.replace(/style="([^"]*)"/g, (match, styles) => {
    const allowed = ["font-size", "color", "font-weight"];
    const validStyles = styles
      .split(";")
      .filter(style => {
        const [prop] = style.split(":");
        return allowed.includes(prop.trim());
      })
      .join(";");
    return validStyles ? `style="${validStyles}"` : "";
  });
  return html;
}
// 删除
const deleteCoupons = row => {
  bodyList.value.cardList = bodyList.value.cardList.filter(
    item => item !== row // 或根据具体字段比较，如 item.id !== row.id
  );
};
const imageUrl = ref("");

const mode = "default";
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
const Title = shallowRef();

// 模拟 ajax 异步获取内容
onMounted(() => {
  chooseCouponValue.value = bodyList.value.cardList;
});

// 监听数据变化，初始化拖拽
watch(
  () => bodyList.value.cardList,
  newVal => {
    if (newVal.length > 0) {
      nextTick(() => {
        initSortable();
      });
    }
  },
  { deep: true }
);

const initSortable = () => {
  const wrapper = document.querySelector(
    "#pure-table-card .el-table__body-wrapper tbody"
  );
  if (wrapper && !wrapper.sortable) {
    // 避免重复初始化
    Sortable.create(wrapper, {
      animation: 300,
      handle: ".drag-btn",
      onEnd: ({ newIndex, oldIndex }) => {
        const currentRow = bodyList.value.cardList.splice(oldIndex, 1)[0];
        bodyList.value.cardList.splice(newIndex, 0, currentRow);
      }
    });
    wrapper.sortable = true; // 标记已初始化
  }
};
const toolbarConfig = {
  excludeKeys: "fullScreen"
};
const titleBar = {
  excludeKeys: [
    "emotion", // 表情
    "insertLink", // 插入链接
    "group-image", // 上传图片
    "group-video", // 上传视频
    "insertTable", // 插入表格
    "codeBlock", // 代码块
    "divider", // 分割线
    "fullScreen" // 全屏
  ]
};

const editorConfig = {
  MENU_CONF: {
    // 禁用自动合并空节点
    mergeAdjacentNodes: false,
    // 配置Word粘贴处理
    pasteFromWord: {
      cleanPaste: true, // 自动清理Word格式
      filterStyle: true // 过滤异常样式
    }
  },
  // 允许的样式
  styleWhitelist: ["font-size", "color", "font-weight"],
  allowedAttributes: {
    span: ["style"],
    p: [],
    b: []
  }
};
// 销毁
const handleDestroy = () => {
  if (editorRef.value) {
    editorRef.value.destroy();
    editorRef.value = null;
  }
};
//富文本框的key
const editorKey = ref(0);
const titleKey = ref(0);
const handleCreated = editor => {
  // 记录 editor 实例，重要！
  handleDestroy();
  editorRef.value = editor;

  // 先确保清空可能存在的旧实例
  setTimeout(() => {
    try {
      const cleanedHtml = cleanEditorContent(couponForm.value.Explain);
      editor.setHtml(cleanedHtml);
    } catch (e) {
      console.error("设置编辑器内容失败:", e);
      editor.setHtml("<p>内容加载错误</p>");
    }
  }, 100);
};
const cleanEditorContent = html => {
  // 1. 移除空标签
  html = html.replace(/<(\w+)[^>]*>\s*<\/\1>/g, "");

  // 2. 简化样式
  html = html.replace(/style="([^"]*)"/g, (match, styles) => {
    const allowed = ["font-size", "color", "font-weight"];
    const validStyles = styles
      .split(";")
      .filter(style => {
        const [prop] = style.split(":");
        return allowed.includes(prop.trim());
      })
      .join(";");
    return validStyles ? `style="${validStyles}"` : "";
  });

  // 3. 移除Word特有属性
  html = html.replace(/<span\s+lang="[^"]*"/g, "<span");

  return html;
};
const titleCreated = editor => {
  // 销毁旧实例
  if (editors.title.instance) {
    editors.title.instance.destroy();
  }

  editors.title.instance = editor;
};
// 监听卡券详情内容变化
const handleExplainChange = () => {
  if (editorRef.value) {
    const content = editorRef.value.getHtml();
    editorRef.value.content = content;
    // 这里可以添加额外的逻辑，比如更新表单数据等
    couponForm.value.Explain = content;
  }
};
// 获取卡券原有细则
const getOriginalRules = async () => {
  const req = ref({
    CardSheetListId: couponForm.value.GrouponKey,
    "Paging.Rows": 20,
    "Paging.Page": "1",
    "Paging.Sidx": "InputTime",
    "Paging.Sord": "desc",
    "Paging.Records": "1"
  });
  const res = await GetCardSheetListRecord(req.value);
  console.log(res.data);
  if (!res.data.list[0]) {
    ElMessage.error("卡券原有细则获取失败");
  }
  couponForm.value.Explain = res.data.list[0].Explain;
};
// 确定修改
const handleChangeCoupon = () => {
  if (currentEditIndex.value >= 0) {
    // 更新原数组中的对应项
    bodyList.value.cardList[currentEditIndex.value] = {
      ...couponForm.value
      // 如果需要清理HTML格式，可以在这里处
    };
    // 重置索引
    currentEditIndex.value = -1;
    // 关闭弹窗
    dialogFormVisible.value = false;
    // 提示修改成功
    ElMessage.success("卡券修改成功");
  } else {
    ElMessage.error("找不到要修改的卡券");
  }
};
// const syncDialog = ref(false);
const editorsRefs = ref([]); // 存储左右两侧编辑器的引用
const editors = reactive({
  title: {
    instance: null,
    key: 0
  }
});

// 初始化编辑器引用数组
const initEditorsRefs = () => {
  editorsRefs.value = bodyList.value.cardList.map(() => ({
    left: null,
    right: null
  }));
};

// 处理编辑器创建事件
const couponCreate = (editor, index, side) => {
  // 确保引用数组结构存在
  if (!editorsRefs.value[index]) {
    editorsRefs.value[index] = {};
  }

  editorsRefs.value[index][side] = editor;

  // 设置初始内容
  const content =
    side === "left"
      ? bodyList.value.cardList[index].Explain
      : cleanEditorContent(bodyList.value.cardList[index].newExplain) || "";

  // 延迟设置内容确保编辑器完全初始化
  setTimeout(() => {
    editor.setHtml(content);

    // 如果是左侧编辑器，设置为只读
    if (side === "left") {
      editor.disable();
    }
  }, 100);
};
// const imageUrl = ref("");
const fileList = ref([]);
const selectedFile = ref(null);
const handleFileChange = (file, fileList) => {
  selectedFile.value = file.raw;

  // 预览图片
  const reader = new FileReader();
  reader.onload = e => {
    imageUrl.value = e.target.result;
    console.log(imageUrl.value);
  };
  reader.readAsDataURL(file.raw);
  console.log(selectedFile.value);
};
// 新旧细则保存
const handleSyncConfirm = () => {
  // 遍历所有卡券
  bodyList.value.cardList.forEach((item, index) => {
    console.log(item);
    // 检查新细则是否存在且不为空
    if (isRichTextNotEmpty(item.newExplain)) {
      // 新细则不为空，更新为新的细则内容
      item.Explain = item.newExplain;
    }
    // 无论是否更新，都删除临时的newExplain字段
    delete item.newExplain;
  });

  // 关闭弹窗
  syncDialog.value = false;

  // 提示用户保存成功
  ElMessage.success("卡券细则同步完成");
};

// 判断富文本内容是否真正有内容（非空）
function isRichTextNotEmpty(html) {
  if (!html) return false;

  // 移除所有HTML标签
  const textOnly = html.replace(/<[^>]+>/g, "");
  // 移除所有空白字符（包括空格、换行等）
  const trimmed = textOnly.replace(/\s+/g, "");

  return trimmed.length > 0;
}

// 表单验证规则
const rules = reactive({
  title: [
    { required: true, message: "请输入活动名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
  ]
});
</script>

<template>
  <div v-loading.fullscreen.lock="saleDialogLoad">
    <el-dialog
      v-model="editVisible"
      :title="title"
      top="0px"
      width="100%"
      style="height: 100%; margin: 0px; overflow-y: auto"
      @close="visibleClose()"
    >
      <div class="container">
        <div class="con-left">
          <div class="left-body">
            <div class="left-top">
              <div class="left-top-tittle">{{ bodyList.title }}</div>
              <!-- <img
                v-if="bodyList.bana"
                src="@/assets/image.png"
                class="left-top-img"
              /> -->
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 180px;
                "
              >
                <img
                  v-if="imageUrl"
                  :src="imageUrl"
                  class="avatar"
                  style="height: 180px; width: 100%"
                />
                <p v-if="imageUrl === ''">图片位置</p>
              </div>
              <div class="left-top-text">
                活动时间
                <span style="color: #f28c15; margin-left: 10px">
                  {{ bodyList.begintime }} -- {{ bodyList.endtime }}</span
                >
              </div>
            </div>
            <div class="left-bottom">
              <div class="left-bottom-item" v-for="item in bodyList.cardList">
                <!-- <div class="left-bottom-item-title">{{ item.GrouponName }}</div> -->
                <div v-html="item.GrouponName"></div>
                <!-- <p class="item-time">{{ item.time }}</p> -->
                <div class="mt-1">
                  <span
                    style="
                      color: #f28c15;
                      margin-right: 3px;
                      font-weight: 700;
                      font-size: 15px;
                    "
                    >{{ item.GrouponPrice }}</span
                  ><del class="item-del">{{ item.GoodsPrice }}</del>
                  <span
                    class="item-del"
                    style="margin-left: 15px"
                    v-if="bodyList.isShowItemCou"
                  >
                    <IconifyIconOnline
                      icon="noto:fire"
                      style="display: inline-block"
                      width="13px"
                      height="13px"
                    />
                    已购0</span
                  >
                  <span style="float: right; padding-bottom: 20px">
                    <el-input-number
                      v-model="num"
                      :min="1"
                      :max="10"
                      size="small"
                      class="input-number"
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="con-right">
          <el-form
            :model="bodyList"
            label-width="auto"
            style="max-width: 80%; height: 100%"
            :rules="rules"
            ref="formRef"
          >
            <el-form-item label="活动名称:" prop="title">
              <el-input
                clearable
                v-model="bodyList.title"
                :disabled="state === 'see'"
                maxlength="50"
                show-word-limit
                style="width: 400px"
              />
            </el-form-item>
            <el-form-item
              label="活动图片:"
              v-if="state !== 'see'"
              prop="imageUrl"
            >
              <el-upload
                class="avatar-uploader"
                :show-file-list="true"
                :auto-upload="false"
                :on-change="handleFileChange"
                :file-list="fileList"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="可用时间:" prop="value2">
              <div style="width: 350px">
                <el-date-picker
                  v-model="form.value2"
                  type="datetimerange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  date-format="YYYY/MM/DD ddd"
                  time-format="A hh:mm:ss"
                  :disabled="state === 'see'"
                  class="custom-date-picker"
                />
              </div>
            </el-form-item>
            <el-form-item label="展示设置:">
              <!-- <el-checkbox
                v-model="bodyList.isShowTime"
                label="展示倒计时"
                size="large"
                :disabled="state === 'see'"
              /> -->
              <el-checkbox
                v-model="bodyList.isShowItemCou"
                label="显示已购数量"
                size="large"
                :disabled="state === 'see'"
              />
              <el-checkbox
                v-model="bodyList.isShowTitleCou"
                label="显示总销售订单数"
                size="large"
                :disabled="state === 'see'"
              />
            </el-form-item>
            <el-form-item
              label="选择卡券:"
              style="min-height: 0px; max-height: 45%; margin-bottom: 10px"
              prop="cardList"
            >
              <el-button
                size="small"
                style="margin-bottom: 10px"
                @click="batchSync()"
                v-if="bodyList.cardList.length > 0 && state !== 'see'"
                >批量修改卡券细则</el-button
              >
              <pure-table
                ref="tableRef"
                :data="bodyList.cardList"
                :columns="columns"
                height="30vh"
                size="small"
                border
                id="pure-table-card"
                v-if="bodyList.cardList.length > 0"
                style="margin-bottom: 10px"
                row-key="GrouponKey"
              >
                <template #GrouponName="{ row }">
                  <div draggable="true">
                    <iconify-icon-online
                      icon="icon-park-outline:drag"
                      class="drag-btn cursor-grab"
                      style="display: inline-block; margin-right: 5px"
                    />
                    <span v-html="row.GrouponName"></span>
                  </div>
                </template>
                <template #operation="{ row }">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="editCoupons(row)"
                  >
                    修改
                  </el-button>
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="deleteCoupons(row)"
                    >删除</el-button
                  >
                </template>
              </pure-table>
              <el-button
                type="primary"
                @click="chooseCoupons()"
                style="margin-bottom: 10px"
                v-if="state !== 'see'"
                >选择卡券信息
              </el-button>
            </el-form-item>
            <el-form-item v-if="state !== 'see'">
              <el-button
                type="primary"
                @click="onSubmit"
                style="margin-top: 10px"
                >保存</el-button
              >
              <el-button @click="visibleClose()" style="margin-top: 10px"
                >取消</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <!-- 修改卡券弹窗 -->
        <el-dialog
          v-model="dialogFormVisible"
          title="修改卡券信息"
          width="80%"
          top="0px"
          class="cutom-dialog"
          style="height: 93%; margin: 0 auto; overflow-y: auto"
          @closed="currentEditIndex = -1"
        >
          <div class="dialog-content">
            <el-form :model="form">
              <el-form-item label="卡券名称:" :label-width="formLabelWidth">
                <div class="wangeditor" style="width: 100%">
                  <Toolbar
                    v-if="editors.title.instance"
                    :editor="editors.title.instance"
                    :defaultConfig="titleBar"
                    :mode="mode"
                    style="border-bottom: 1px solid #ccc"
                  />
                  <Editor
                    v-model="couponForm.GrouponName"
                    :key="'title-' + editors.title.key"
                    :defaultConfig="editorConfig"
                    :mode="mode"
                    style="height: 100px; overflow-y: auto"
                    @onCreated="titleCreated"
                  />
                </div>
              </el-form-item>
              <el-row>
                <el-col :span="4">
                  <el-form-item
                    label="售价:"
                    :label-width="formLabelWidth"
                    prop="GrouponPrice"
                    inline
                  >
                    <el-input
                      type="number"
                      v-model="couponForm.GrouponPrice"
                      autocomplete="off"
                      oninput="if(value.length > 10) value = value.slice(0, 10)"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4"
                  ><el-form-item
                    label="门市价:"
                    :label-width="formLabelWidth"
                    prop="GoodsPrice"
                  >
                    <el-input
                      type="number"
                      v-model="couponForm.GoodsPrice"
                      autocomplete="off"
                      oninput="if(value.length > 10) value = value.slice(0, 10)"
                    /> </el-form-item
                ></el-col>
                <el-col :span="4">
                  <el-form-item
                    label="卡券数量:"
                    :label-width="formLabelWidth"
                    style="margin-bottom: 0px"
                    oninput="if(value.length > 10) value = value.slice(0, 10)"
                  >
                    <el-input
                      type="number"
                      v-model="couponForm.qty"
                      autocomplete="off"
                      oninput="if(value.length > 10) value = value.slice(0, 10)"
                    />
                  </el-form-item>
                </el-col>
                <span
                  style="
                    font-size: smaller;
                    padding-top: 13px;
                    padding-left: 10px;
                  "
                >
                  *注意:如果是四次卡请输入卡券数量为四，我们会为用户派发四张当前卡券
                </span>
              </el-row>

              <el-form-item label="卡券细则:" :label-width="formLabelWidth">
                <button
                  style="
                    background-color: #409eff;
                    color: white;
                    width: 100px;
                    border-radius: 5px;
                    font-size: 12px;
                    display: inline-block;
                  "
                  @click="getOriginalRules()"
                >
                  获取原有细则
                </button>
                <div class="wangeditor" style="width: 100%">
                  <Toolbar
                    :editor="editorRef"
                    :defaultConfig="titleBar"
                    :mode="mode"
                  />
                  <Editor
                    :key="editorKey"
                    :defaultConfig="editorConfig"
                    :mode="mode"
                    style="
                      height: 41vh;
                      overflow-y: auto;
                      border: 1px solid #ccc;
                    "
                    @onCreated="handleCreated"
                    @onChange="handleExplainChange"
                  />
                </div>
              </el-form-item>
            </el-form>
          </div>
          <!-- <template #footer> -->
          <div style="float: right">
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="handleChangeCoupon">
              确定
            </el-button>
          </div>
          <!-- </template> -->
        </el-dialog>
      </div>
      <CouponDialog
        v-model:couponDialog="couponDialog"
        v-model:couponState="couponState"
      />
    </el-dialog>

    <!--同步卡券细则弹窗  -->
    <el-dialog
      v-model="syncDialog"
      title="批量修改卡券细则"
      width="80%"
      top="0px"
      style="height: 94%; margin: 0 auto; overflow-y: auto"
    >
      <!-- <div style=> -->
      <el-scrollbar height="95%">
        <el-row :gutter="20" style="width: 100%">
          <el-col :span="12">
            <h4 style="text-align: center">卡券原有细则</h4>
            <div
              style="padding-top: 10px"
              class="editor-container"
              v-for="(item, index) in bodyList.cardList"
              :key="`left-${index}`"
            >
              <h4 v-html="item.GrouponName"></h4>
              <Toolbar
                :editor="editorsRefs[index].left"
                :defaultConfig="titleBar"
                :mode="mode"
              />
              <Editor
                :defaultConfig="editorConfig"
                :mode="mode"
                style="height: 300px; overflow-y: auto; border: 1px solid #ccc"
                @onCreated="editor => couponCreate(editor, index, 'left')"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <h4 style="text-align: center">卡券新细则</h4>
            <div
              style="padding-top: 10px"
              class="editor-container"
              v-for="(item, index) in bodyList.cardList"
              :key="`right-${index}`"
            >
              <h4 v-html="item.GrouponName"></h4>
              <Toolbar
                :editor="editorsRefs[index].right"
                :defaultConfig="titleBar"
                :mode="mode"
              />
              <Editor
                v-model="item.newExplain"
                :defaultConfig="editorConfig"
                :mode="mode"
                style="height: 300px; overflow-y: auto; border: 1px solid #ccc"
                @onCreated="editor => couponCreate(editor, index, 'right')"
              />
            </div>
          </el-col>
        </el-row>
      </el-scrollbar>
      <!-- <template #footer> -->
      <!-- 使用 footer slot 控制按钮位置 -->
      <div style="float: right">
        <el-button @click="syncDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSyncConfirm">
          确定修改
        </el-button>
      </div>
      <!-- </template> -->
    </el-dialog>
  </div>
</template>

<style lang="scss">
.container {
  width: 100%;
  height: 100% !important;
  display: flex;
  margin: 0 auto;
  overflow: auto;
}

.el-dialog__body {
  height: 90% !important;
}
.avatar-uploader .avatar {
  width: 200px;
  height: 120px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 120px;
  text-align: center;
}
</style>
<style scoped>
.con-left {
  width: 40%;
  height: 100%;
}
.draggable-handle {
  cursor: move;
  user-select: none;
}
.custom-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

.dialog-content {
  flex: 1;
  height: 100%;
  overflow-y: auto;
}

/* 确保编辑器高度适应 */
.wangeditor {
  height: auto !important;
  max-height: 50vh;
}

/* 拖拽时的视觉效果 */
.pure-table tr.drop-over-downward td {
  border-bottom: 2px dashed #1890ff;
}

.pure-table tr.drop-over-upward td {
  border-top: 2px dashed #1890ff;
}

.con-right {
  flex: 1;
  height: 100%;
  background-color: #fff;
  margin-left: 25px;
}

.left-body {
  width: 80%;
  margin: 0px auto;
  height: 100%;
  color: black;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
}

.left-top {
  width: 100%;
}

.left-top-tittle {
  background-color: #fff;
  text-align: center;
  font-size: 15px;
  font-weight: 700;
  color: black;
  padding: 5px 0px;
}

.left-top-img {
  width: 100%;
}

.left-top-text {
  background-color: #fff;
  padding: 10px;
}

.left-bottom {
  max-height: 55%;
  overflow-y: auto;
}

.left-bottom-item {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
}

.left-bottom-item-title {
  font-weight: 700;
  color: black;
  font-size: 15px;
}

.item-time {
  font-weight: 700;
}

.item-del {
  font-size: 12px;
  color: #a3a3a3;
}

.input-number {
  width: 100px;
}
</style>
