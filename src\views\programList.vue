<script setup>
import { onMounted, watch, ref, nextTick } from "vue";
import { GridStack } from 'gridstack';
import 'gridstack/dist/gridstack.min.css';
const dialogFormVisible = ref(false);
const contentList = ref([]);
const gird = ref(null); // 统一使用这个ref管理实例
const gridData = [];
const addWidgetContent = ref(0); //为添加的小部件计数
defineOptions({
  // name 作为一种规范最好必须写上并且和路由的name保持一致
  name: "ProgramList"
});
const GridStackInit = () => {
  // 确保DOM已经渲染
  const gridElement = document.querySelector('.grid-stack');
  if (!gridElement) {
    console.error('GridStack容器未找到');
    return;
  }

  const options = {
    dragOut: true,
    margin: 8,
    allowHtml: true,
    column: 12,
    maxRow: 6,
    removable: "#trash",
    removeTimeout: 100,
    acceptWidgets: '.grid-stack-item',
    dragInOptions: {
      revert: "invalid",
      scroll: false,
      appendTo: "body",
      helper: "clone",
    },
  };

  GridStack.renderCB = (el, node) => {
    el.innerHTML = node.content || "";
  };

  // 关键修改：将实例赋值给gird.value
  gird.value = GridStack.init(options);

  if (gird.value) {
    gird.value.load(gridData);
  }
};

const addGrid = () => {
  if (gird.value) {
    addWidgetContent.value += 1; // 增加计数
    gird.value.addWidget({ w: 1, h: 6, content: `部件${addWidgetContent.value}` });
  }
};

const saveLayout = () => {
  if (gird.value) {
    const nodes = gird.value.save();
    console.log('当前布局:', nodes);
  }
};

// 监听弹窗打开事件
watch(dialogFormVisible, async (newVal) => {
  if (newVal) {
    await nextTick();
    if (!gird.value) {
      GridStackInit();
    }


    // 设置拖放事件
    const container = document.querySelector('.right-box');
    if (container) {
      container.ondragover = (e) => {
        e.preventDefault()
      };
      container.addEventListener('drop', addGrid);
    }

    const deleteGrid = document.querySelector('.delete-grid');
    if (deleteGrid) {
      deleteGrid.ondragover = (e) => e.preventDefault();
      deleteGrid.addEventListener('drop', (e) => { console.log(e) });
    }
  }
});

// 表单
const form = ref({
  name: '',
  shopName: "",
  programList: "",
  programLayout: ""
});


// 应用预设布局的方法
// const applyPresetLayout = (layoutType) => {
//   if (layoutType === 'twoColumn') {
//     console.log('选择两列的布局');

//     // 应用二分布局
//   } else if (layoutType === 'threeColumn') {
//     // 应用三分布局
//     console.log('选择三列的布局');
//   }
// }

const onSubmit = () => {
  console.log('submit!', form);
};
</script>





<template>
  <div style="width: 100%;">
    <el-form :model="form" label-width="auto"
      style="max-width: 50%;height: 100%; background-color:white; padding: 30px;">
      <p style="text-align: center;margin-bottom: 20px;color: grey;">添加节目清单</p>
      <el-form-item label="名称:">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="选择店铺:">
        <el-select v-model="form.shopName" placeholder="选择放置节目的店铺">
          <el-option label="Zone one" value="shanghai" />
          <el-option label="Zone two" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="节目清单:">
        <el-select v-model="form.programList" placeholder="请选择想要播放的节目清单">
          <el-option label="节目一" value="shanghai" />
          <el-option label="节目二" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="节目布局:">
        <el-select v-model="form.programLayout" placeholder="请选择布局方式">
          <el-option label="二分布局" value="twoColumn" />
          <el-option label="三分布局" value="threeColumn" />
          <el-option label="自定义布局" value="auto" />
        </el-select>
      </el-form-item>
      <el-button @click="dialogFormVisible = true" style="margin-left:70px;margin-bottom: 20px;">设置自定义布局</el-button>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
      </el-form-item>
    </el-form>
    <!-- <div
      style="display: inline-block; width: 45%; margin-left: 20px; background-color: white; height: 100%; padding: 20px;">
      测试内容
    </div> -->
    <!-- 自定义布局弹窗 -->
    <el-dialog v-model="dialogFormVisible" title="自定义布局" fullscreen width="100%" draggable>
      <!-- 编辑布局 -->
      <div id="home">
        <!-- 左边的添加与删除 -->
        <div class="left-box">
          <div id="trash" class="delete-grid">放在这里删除小部件！</div>
          <div class="add-grid" draggable="true">拖入添加小部件！</div>
          <!-- <button @click="AddListClick()"> 增加</button> -->
        </div>
        <!-- 右边的grid容器 -->
        <div class="right-box">
          <h2 style="text-align: center;">操作布局面板</h2>
          <el-button type="primary" @click="saveLayout()"
            style="width: 100px; height: 35px;border-radius: 10px;">保存</el-button>
          <div class="grid-stack" id="advanced-grid" style="border: 2px dashed lightgrey;">
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>







<style lang="scss" scoped>
body {
  padding: 0;
  margin: 0;
}

#home {
  display: flex;
  width: 100%;
  height: calc(100vh - 34px - 68px);
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}

.grid-stack {
  /* 根据页面结构调整 */
  height: 78vh;
}

.left-box {
  padding-top: 10px;
  display: inline;
  display: flex;
  flex-direction: column;
  width: 15%;
  border-right: 2px solid #ebebeb;
  background-color: rgb(49, 49, 146);

  .delete-grid {
    width: 80%;
    height: 20%;
    background-color: #ffffff;
    text-align: center;
    border: 2px solid #ffffff;
    margin: 10px auto;
  }

  .add-grid {
    width: 80%;
    height: 120px;
    background-color: #54ff9f;
    border: 2px solid #ffffff;
    margin: 0 0 0 20px;
  }
}

.right-box {
  width: 85%;
  background-color: #ebebeb;
}
</style>
<style lang="scss">
.grid-stack-item-content {
  background-color: #ffffff !important;
  text-align: center;
}

.grid-stack-item-content {
  padding: 0 !important;
  /* 去除内边距 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
}
</style>
