import type { RoomStatus, Room, RoomAction } from "../types/room";

// 获取状态卡片样式类
export const getStatusCardClass = (color: string): string => {
  const classes: Record<string, string> = {
    orange: "bg-orange-50 text-orange-600 hover:bg-orange-100",
    green: "bg-green-50 text-green-600 hover:bg-green-100",
    red: "bg-red-50 text-red-600 hover:bg-red-100",
    blue: "bg-blue-50 text-blue-600 hover:bg-blue-100",
    purple: "bg-purple-50 text-purple-600 hover:bg-purple-100",
    gray: "bg-gray-50 text-gray-600 hover:bg-gray-100"
  };
  return classes[color] || classes.gray;
};

// 获取房间状态样式类
export const getRoomStatusClass = (status: RoomStatus): string => {
  const statusClasses: Record<RoomStatus, string> = {
    occupied: "bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100",
    free: "bg-green-50 border-green-200 text-green-800 hover:bg-green-100",
    cleaning: "bg-red-50 border-red-200 text-red-800 hover:bg-red-100",
    booked: "bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100",
    maintenance: "bg-purple-50 border-purple-200 text-purple-800 hover:bg-purple-100",
    disabled: "bg-gray-50 border-gray-200 text-gray-800 hover:bg-gray-100"
  };
  return statusClasses[status] || statusClasses.free;
};

// 获取状态文本
export const getStatusText = (status: RoomStatus): string => {
  const statusTexts: Record<RoomStatus, string> = {
    occupied: "占用中",
    free: "可用",
    cleaning: "待清洁",
    booked: "已预订",
    maintenance: "维修中",
    disabled: "已停用"
  };
  return statusTexts[status] || "未知";
};

// 获取状态标签类型
export const getStatusTagType = (status: RoomStatus): string => {
  const tagTypes: Record<RoomStatus, string> = {
    occupied: "warning",
    free: "success",
    cleaning: "danger",
    booked: "primary",
    maintenance: "info",
    disabled: "info"
  };
  return tagTypes[status] || "";
};

// 获取房间可用操作
export const getRoomActions = (status: RoomStatus): RoomAction[] => {
  const actions: Record<RoomStatus, RoomAction[]> = {
    free: [
      { key: "checkin", label: "开房", type: "primary", icon: "clock" },
      { key: "book", label: "预订", type: "", icon: "plus" }
    ],
    occupied: [
      { key: "edit-info", label: "改资料", type: "", icon: "edit" },
      { key: "place-order", label: "落单", type: "", icon: "plus-circle" },
      { key: "add-time", label: "加钟", type: "", icon: "clock" },
      { key: "gift", label: "赠送", type: "", icon: "gift" },
      { key: "cancel-item", label: "消单", type: "warning", icon: "trash-alt" },
      { key: "bill", label: "账单", type: "", icon: "file-invoice-dollar" },
      { key: "continue-order", label: "续单", type: "", icon: "history" },
      { key: "discount", label: "打折", type: "", icon: "tags" },
      { key: "list", label: "清单", type: "", icon: "list-alt" },
      { key: "transfer", label: "转房", type: "", icon: "exchange-alt" },
      { key: "change-gift", label: "改赠", type: "", icon: "gifts" },
      { key: "checkout", label: "结账", type: "primary", icon: "credit-card" },
    ],
    booked: [
      { key: "checkin", label: "入住", type: "primary", icon: "clock" },
      { key: "cancel", label: "取消", type: "danger", icon: "times" }
    ],
    cleaning: [
      { key: "clean-done", label: "清洁完成", type: "success", icon: "check" }
    ],
    maintenance: [
      { key: "repair-done", label: "维修完成", type: "success", icon: "wrench" }
    ],
    disabled: [
      { key: "enable", label: "启用", type: "primary", icon: "power-off" }
    ]
  };
  return actions[status] || [];
};

// 计算房间状态统计
export const calculateStatusCounts = (rooms: Room[]) => {
  const counts: Record<RoomStatus, number> = {
    occupied: 0,
    free: 0,
    cleaning: 0,
    booked: 0,
    maintenance: 0,
    disabled: 0
  };

  rooms.forEach(room => {
    counts[room.status]++;
  });

  return counts;
};

// 过滤房间
export const filterRooms = (
  rooms: Room[],
  searchKeyword: string,
  currentArea: string
): Room[] => {
  return rooms.filter(room => {
    const matchesSearch = searchKeyword === "" || 
      room.id.toLowerCase().includes(searchKeyword.toLowerCase());
    const matchesArea = currentArea === "all" || room.area === currentArea;
    return matchesSearch && matchesArea;
  });
};
