<template>
  <div
    class="cashier-system min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50"
  >
    <!-- 顶部导航栏 -->
    <cashier-header
      v-model:search-keyword="searchKeyword"
      @search="handleSearch"
    />

    <!-- 主内容区域 -->
    <main class="flex-grow container mx-auto p-4 sm:p-6">
      <div class="grid grid-cols-12 gap-6">
        <!-- 左侧信息栏 -->
        <div class="col-span-12 lg:col-span-2">
          <info-sidebar
            :room-statuses="roomStatuses"
            :quick-actions="quickActions"
            :recent-openings="recentOpenings"
            :current-status-filter="currentStatusFilter"
            @filter-by-status="handleStatusFilter"
            @quick-action="handleQuickAction"
          />
        </div>

        <!-- 中间核心区 -->
        <div class="col-span-12 lg:col-span-7">
          <!-- 区域筛选 -->
          <area-filter
            :areas="areas"
            :current-area="currentArea"
            @switch-area="switchArea"
          />

          <!-- 房间状态网格 -->
          <room-grid
            :filtered-areas="filteredAreasWithRooms"
            :selected-room="selectedRoom"
            @select-room="selectRoom"
            @room-action="handleRoomAction"
          />
        </div>

        <!-- 右侧详情面板 -->
        <aside class="col-span-12 lg:col-span-3">
          <div
            class="bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-sm border border-slate-200 sticky top-24"
            style="
              height: calc(100vh - 8rem);
              max-height: 800px;
              min-height: 600px;
            "
          >
            <!-- 默认状态 -->
            <div
              v-if="!selectedRoom"
              class="flex flex-col items-center justify-center h-full text-center"
            >
              <div
                class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4"
              >
                <font-awesome-icon
                  icon="mouse-pointer"
                  class="text-3xl text-gray-400"
                />
              </div>
              <h4 class="text-lg font-medium text-gray-600 mb-2">选择房间</h4>
              <p class="text-gray-500 text-sm">请点击左侧房间查看详细信息</p>
            </div>

            <!-- 选中房间详情 -->
            <div v-else class="flex flex-col h-full space-y-4">
              <!-- 房间信息头部 -->
              <div class="flex-shrink-0 border-b border-gray-200 pb-4">
                <div class="flex justify-between items-start mb-3">
                  <div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-1">
                      房间 {{ selectedRoom.id }}
                    </h3>
                    <p class="text-sm text-gray-600">{{ selectedRoom.type }}</p>
                  </div>
                  <span
                    :class="[
                      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                      getStatusTagClass(selectedRoom.status)
                    ]"
                  >
                    {{ getStatusText(selectedRoom.status) }}
                  </span>
                </div>
              </div>

              <!-- 房间详细信息 -->
              <div
                v-if="selectedRoom.status === 'occupied'"
                class="flex-shrink-0 space-y-3"
              >
                <div class="bg-blue-50 rounded-lg p-3">
                  <h4 class="font-medium text-blue-800 mb-2">消费信息</h4>
                  <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                      <span class="text-gray-600">客户：</span>
                      <span class="font-medium">{{
                        selectedRoom.customer
                      }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">开始时间：</span>
                      <span>{{ selectedRoom.startTime }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">已用时长：</span>
                      <span>{{ selectedRoom.duration }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">当前消费：</span>
                      <span class="font-bold text-orange-600"
                        >¥{{ selectedRoom.total }}</span
                      >
                    </div>
                  </div>
                </div>

                <!-- 消费明细 -->
                <div
                  v-if="selectedRoom.items"
                  class="bg-gray-50 rounded-lg p-3"
                >
                  <h4 class="font-medium text-gray-800 mb-2">消费明细</h4>
                  <div class="space-y-1">
                    <div
                      v-for="item in selectedRoom.items"
                      :key="item.id"
                      class="flex justify-between text-sm"
                    >
                      <span>{{ item.name }} × {{ item.quantity }}</span>
                      <span>¥{{ item.total }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex-grow flex flex-col justify-end">
                <div class="grid grid-cols-3 gap-2">
                  <button
                    v-for="action in getRoomActions(selectedRoom.status)"
                    :key="action.key"
                    :class="[
                      'flex items-center justify-center px-2 py-2.5 rounded-lg font-medium transition-all duration-200 text-sm',
                      getActionButtonClass(action.type),
                      { 'col-span-3 !py-3 text-base': action.key === 'checkout' }
                    ]"
                    @click="handleRoomAction(action.key, selectedRoom)"
                  >
                    <font-awesome-icon :icon="action.icon" class="mr-2" />
                    {{ action.label }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </aside>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";

// 导入组件
import CashierHeader from "./components/layout/cashier-header.vue";
import InfoSidebar from "./components/layout/info-sidebar.vue";
import AreaFilter from "./components/room/area-filter.vue";
import RoomGrid from "./components/room/room-grid.vue";

// 导入 composables
import { useRoomData } from "./composables/use-room-data";
import { useRoomOperations } from "./composables/use-room-operations";
import { useSearchFilter } from "./composables/use-search-filter";

// 导入常量和工具函数
import {
  QUICK_ACTIONS,
  RECENT_OPENINGS,
  AREA_DISPLAY_CONFIG
} from "./constants/room-config";
import { getStatusText, getRoomActions } from "./utils/room-helpers";
import type { RoomStatus } from "./types/room";

defineOptions({
  name: "CashierIndex"
});

// 使用 composables
const { allRooms, roomStatuses, selectedRoom, selectRoom } = useRoomData();

const { handleRoomAction } = useRoomOperations();

const {
  searchKeyword,
  currentArea,
  areas,
  handleSearch,
  switchArea,
  filterByStatus
} = useSearchFilter();

// 当前状态筛选
const currentStatusFilter = ref("");

// 常量数据
const quickActions = QUICK_ACTIONS;
const recentOpenings = RECENT_OPENINGS;

// 获取状态标签样式
const getStatusTagClass = (status: RoomStatus): string => {
  const tagClasses: Record<RoomStatus, string> = {
    occupied: "bg-orange-100 text-orange-800",
    free: "bg-green-100 text-green-800",
    cleaning: "bg-red-100 text-red-800",
    booked: "bg-blue-100 text-blue-800",
    maintenance: "bg-purple-100 text-purple-800",
    disabled: "bg-gray-100 text-gray-800"
  };
  return tagClasses[status] || tagClasses.free;
};

// 获取操作按钮样式
const getActionButtonClass = (type: string): string => {
  const buttonClasses: Record<string, string> = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white shadow-md",
    success: "bg-green-600 hover:bg-green-700 text-white shadow-md",
    danger: "bg-red-600 hover:bg-red-700 text-white shadow-md",
    warning: "bg-orange-600 hover:bg-orange-700 text-white shadow-md",
    "": "bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-300"
  };
  return buttonClasses[type] || buttonClasses[""];
};

// 计算属性 - 带房间数据的区域配置
const filteredAreasWithRooms = computed(() => {
  return Object.values(AREA_DISPLAY_CONFIG)
    .map(area => ({
      ...area,
      rooms: allRooms.filter(room => {
        // 首先按区域筛选 - 每个区域只显示属于该区域的房间
        const matchesArea = room.area === area.key;

        // 然后按搜索关键词筛选
        const matchesSearch =
          searchKeyword.value === "" ||
          room.id.toLowerCase().includes(searchKeyword.value.toLowerCase());

        // 最后按状态筛选（如果有激活的状态筛选）
        const matchesStatus =
          currentStatusFilter.value === "" ||
          room.status === currentStatusFilter.value;

        return matchesArea && matchesSearch && matchesStatus;
      })
    }))
    .filter(area => {
      // 只有在选择了特定区域时才过滤区域，否则显示所有区域
      return currentArea.value === "all" || area.key === currentArea.value;
    });
});

// 处理状态筛选
const handleStatusFilter = (status: string) => {
  if (currentStatusFilter.value === status) {
    // 如果点击的是当前激活的状态，则取消筛选
    currentStatusFilter.value = "";
  } else {
    // 否则设置新的状态筛选
    currentStatusFilter.value = status;
  }
};

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case "booking":
      ElMessage.info("预订功能开发中...");
      break;
    case "member":
      ElMessage.info("会员功能开发中...");
      break;
    case "report":
      ElMessage.info("报表功能开发中...");
      break;
  }
};
</script>

<style scoped>
/* 收银系统专用样式 */
.cashier-system {
  font-family: "Inter", "Noto Sans SC", sans-serif;
}

/* 房间卡片样式 */
.room-card {
  transition: all 0.2s ease-in-out;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义按钮样式 */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-warning {
  @apply bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* 自定义输入框样式 */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

/* 自定义标签样式 */
.tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.tag-primary {
  @apply bg-blue-100 text-blue-800;
}

.tag-success {
  @apply bg-green-100 text-green-800;
}

.tag-warning {
  @apply bg-orange-100 text-orange-800;
}

.tag-danger {
  @apply bg-red-100 text-red-800;
}

.tag-info {
  @apply bg-gray-100 text-gray-800;
}
</style>
