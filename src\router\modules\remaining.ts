const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/system-selector",
    name: "SystemSelector",
    component: () => import("@/views/system-selector/index.vue"),
    meta: {
      title: "系统选择",
      showLink: false,
      rank: 102,
      requiresAuth: true
    }
  },
  {
    path: "/cashier",
    name: "CashierSystem",
    component: () => import("@/views/cashier/index.vue"),
    meta: {
      title: "KTV收银系统",
      showLink: false,
      rank: 103,
      requiresAuth: true
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 104
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
