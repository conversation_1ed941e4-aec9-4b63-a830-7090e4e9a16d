import { defineStore } from "pinia";
import { ref, reactive, computed } from "vue";
import type { Room, RoomStatusConfig, AreaConfig } from "@/views/cashier/types/room";
import { MOCK_ROOMS, ROOM_STATUS_CONFIG, AREA_DISPLAY_CONFIG } from "@/views/cashier/constants/room-config";
import { calculateStatusCounts, filterRooms } from "@/views/cashier/utils/room-helpers";

/**
 * 收银系统 - 房间管理 Store
 */
export const useCashierRoomStore = defineStore("cashier-room", () => {
  // ==================== 状态 ====================
  const allRooms = reactive<Room[]>([...MOCK_ROOMS]);
  const roomStatuses = reactive<RoomStatusConfig[]>([...ROOM_STATUS_CONFIG]);
  const selectedRoom = ref<Room | null>(null);
  
  // 搜索和筛选状态
  const searchKeyword = ref("");
  const currentArea = ref("all");
  
  // UI 状态
  const isLoading = ref(false);
  const lastUpdateTime = ref<Date>(new Date());

  // ==================== 计算属性 ====================
  
  // 过滤后的房间列表
  const filteredRooms = computed(() => {
    return filterRooms(allRooms, searchKeyword.value, currentArea.value);
  });

  // 区域配置（带房间数据）
  const areasWithRooms = computed(() => {
    return Object.values(AREA_DISPLAY_CONFIG).map(area => ({
      ...area,
      rooms: allRooms.filter(room => 
        (currentArea.value === "all" || room.area === area.key) &&
        (searchKeyword.value === "" || 
         room.id.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    }));
  });

  // 当前选中房间的详细信息
  const selectedRoomDetails = computed(() => {
    if (!selectedRoom.value) return null;
    
    return {
      ...selectedRoom.value,
      // 可以在这里添加计算属性，如总消费、时长等
      totalAmount: selectedRoom.value.total || 0,
      duration: selectedRoom.value.duration || "0分钟"
    };
  });

  // 房间统计信息
  const roomStatistics = computed(() => {
    const total = allRooms.length;
    const occupied = allRooms.filter(room => room.status === "occupied").length;
    const free = allRooms.filter(room => room.status === "free").length;
    const cleaning = allRooms.filter(room => room.status === "cleaning").length;
    
    return {
      total,
      occupied,
      free,
      cleaning,
      occupancyRate: total > 0 ? Math.round((occupied / total) * 100) : 0
    };
  });

  // ==================== 方法 ====================

  // 更新状态统计
  const updateStatusCounts = () => {
    const counts = calculateStatusCounts(allRooms);
    roomStatuses.forEach(status => {
      status.count = counts[status.key];
    });
    lastUpdateTime.value = new Date();
  };

  // 根据房间ID查找房间
  const findRoomById = (roomId: string): Room | undefined => {
    return allRooms.find(room => room.id === roomId);
  };

  // 更新房间状态
  const updateRoomStatus = (roomId: string, newStatus: Room["status"]) => {
    const room = findRoomById(roomId);
    if (room) {
      room.status = newStatus;
      updateStatusCounts();
      
      // 如果更新的是当前选中的房间，同步更新选中状态
      if (selectedRoom.value?.id === roomId) {
        selectedRoom.value = { ...room };
      }
    }
  };

  // 更新房间信息
  const updateRoomInfo = (roomId: string, updates: Partial<Room>) => {
    const room = findRoomById(roomId);
    if (room) {
      Object.assign(room, updates);
      updateStatusCounts();
      
      // 同步更新选中房间
      if (selectedRoom.value?.id === roomId) {
        selectedRoom.value = { ...room };
      }
    }
  };

  // 选择房间
  const selectRoom = (room: Room) => {
    selectedRoom.value = { ...room };
  };

  // 清除选择
  const clearSelection = () => {
    selectedRoom.value = null;
  };

  // 搜索房间
  const searchRooms = (keyword: string) => {
    searchKeyword.value = keyword;
  };

  // 切换区域
  const switchArea = (areaKey: string) => {
    currentArea.value = areaKey;
  };

  // 按状态筛选
  const filterByStatus = (status: Room["status"]) => {
    // 这里可以实现按状态筛选的逻辑
    console.log("筛选状态:", status);
  };

  // 刷新房间数据
  const refreshRooms = async () => {
    isLoading.value = true;
    try {
      // 这里应该调用 API 获取最新房间数据
      // const response = await getRoomsApi();
      // allRooms.splice(0, allRooms.length, ...response.data);
      
      // 模拟 API 调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateStatusCounts();
    } catch (error) {
      console.error("刷新房间数据失败:", error);
    } finally {
      isLoading.value = false;
    }
  };

  // 重置所有状态
  const resetState = () => {
    selectedRoom.value = null;
    searchKeyword.value = "";
    currentArea.value = "all";
    // 重置房间数据到初始状态
    allRooms.splice(0, allRooms.length, ...MOCK_ROOMS);
    updateStatusCounts();
  };

  // ==================== 初始化 ====================
  updateStatusCounts();

  return {
    // 状态
    allRooms,
    roomStatuses,
    selectedRoom,
    searchKeyword,
    currentArea,
    isLoading,
    lastUpdateTime,
    
    // 计算属性
    filteredRooms,
    areasWithRooms,
    selectedRoomDetails,
    roomStatistics,
    
    // 方法
    updateStatusCounts,
    findRoomById,
    updateRoomStatus,
    updateRoomInfo,
    selectRoom,
    clearSelection,
    searchRooms,
    switchArea,
    filterByStatus,
    refreshRooms,
    resetState
  };
});

// 便捷的 hook 函数
export const useCashierRoom = () => useCashierRoomStore();
