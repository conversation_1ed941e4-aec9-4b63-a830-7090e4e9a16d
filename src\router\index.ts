// import "@/utils/sso";
import Cookies from "js-cookie";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { buildHierarchyTree } from "@/utils/tree";
import remainingRouter from "./modules/remaining";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { isUrl, openLink, storageLocal, isAllEmpty } from "@pureadmin/utils";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes,
  addPathMatch
} from "./utils";
import {
  type Router,
  createRouter,
  type RouteRecordRaw,
  type RouteComponent
} from "vue-router";
import {
  type DataInfo,
  userKey,
  removeToken,
  multipleTabsKey,
  getToken
} from "@/utils/auth";

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);

/** 原始静态路由（未做任何处理） */
const routes = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
).concat(...remainingRouter);

/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path;
});

/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

/** 路由白名单 */
const whiteList = ["/login"];

const { VITE_HIDE_HOME } = import.meta.env;


router.beforeEach((to: ToRouteType, from, next) => {
  const token = getToken();

  // 定义特殊路由路径
  const systemSelectorPath = '/system-selector';
  const cashierPath = '/cashier';
  const independentRoutes = [systemSelectorPath, cashierPath]; // 独立页面路由

  // 处理未登录用户
  if (!token) {
    if (whiteList.includes(to.path)) {
      return next();
    }
    removeToken();
    return next({ path: "/login" });
  }

  // 登录后从登录页跳转到系统选择页面
  if (from.path === '/login' && to.path !== systemSelectorPath) {
    return next(systemSelectorPath);
  }

  // 处理独立页面路由（收银系统、系统选择页面）
  if (independentRoutes.includes(to.path)) {
    NProgress.start();

    // 设置页面标题
    const pageTitle = to.path === cashierPath ? "KTV收银系统" : "系统选择";
    const appTitle = getConfig().Title || "管理系统";
    document.title = `${pageTitle} | ${appTitle}`;

    return next();
  }

  // 处理管理系统路由（需要动态路由加载）
  const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
  NProgress.start();

  // 权限检查
  if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
    return next({ path: "/error/403" });
  }

  // 隐藏首页配置
  if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
    return next({ path: "/error/404" });
  }

  // 外部链接处理
  const externalLink = isUrl(to?.name as string);
  if (externalLink) {
    openLink(to?.name as string);
    NProgress.done();
    return next(false);
  }

  // 设置页面标题
  if (!externalLink) {
    to.matched.some(item => {
      if (!item.meta.title) return "";
      const Title = getConfig().Title;
      document.title = Title
        ? `${item.meta.title} | ${Title}`
        : item.meta.title as string;
    });
  }

  // 页面刷新时重新加载动态路由
  const isRefreshing = !from.name;
  if (isRefreshing && to.path !== "/") {
    window.sessionStorage.setItem('routeLoaded', 'true');

    return initRouter().then((router) => {
      // 处理标签页缓存
      if (!useMultiTagsStoreHook().getMultiTagsCache) {
        const route = findRouteByPath(to.path, router.options.routes[0].children);
        getTopMenu(true);

        if (route && route.meta?.title) {
          const tagInfo = isAllEmpty(route.parentId) && route.meta?.backstage
            ? route.children[0]
            : route;

          useMultiTagsStoreHook().handleTags("push", {
            path: tagInfo.path,
            name: tagInfo.name,
            meta: tagInfo.meta,
          });
        }
      }

      // 修正路由名称为空的情况
      if (isAllEmpty(to.name)) {
        return next({ ...to, replace: true });
      }

      return next();
    }).catch((err) => {
      console.error("路由初始化失败:", err);
      window.sessionStorage.removeItem('routeLoaded');
      return next("/error/404");
    });
  }

  // 正常导航
  return next();
});
router.afterEach(() => {
  NProgress.done();
});

export default router;
