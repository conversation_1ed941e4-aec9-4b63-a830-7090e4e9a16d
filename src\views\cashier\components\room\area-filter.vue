<template>
  <div class="mb-6">
    <div
      class="flex flex-wrap gap-2 bg-white/90 backdrop-blur-sm p-2 rounded-xl border border-slate-200"
    >
      <button
        v-for="area in areas"
        :key="area.key"
        :class="[
          'flex-1 text-center px-4 py-2 rounded-lg font-medium transition-all duration-200',
          currentArea === area.key
            ? 'bg-blue-600 text-white shadow-md'
            : 'bg-slate-100 text-slate-700 hover:bg-slate-200 hover:text-slate-800'
        ]"
        @click="switchArea(area.key)"
      >
        {{ area.label }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AreaConfig } from "../../types/room";

defineOptions({
  name: "AreaFilter"
});

// Props
interface Props {
  areas: AreaConfig[];
  currentArea: string;
}

defineProps<Props>();

// Emits
const emit = defineEmits<{
  switchArea: [area: string];
}>();

// 切换区域
const switchArea = (area: string) => {
  emit("switchArea", area);
};
</script>
