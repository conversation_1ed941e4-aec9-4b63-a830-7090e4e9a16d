<template>
  <div class="store-list-container" style="margin: 0; padding: 20px">
    <div class="search-container" style="margin-bottom: 20px">
      <el-date-picker
        v-model="searchDate"
        type="date"
        placeholder="请选择日期"
        value-format="YYYY-MM-DD"
        class="small-font"
      />
      <el-button type="success" @click="handleDailyStats"
        >统计当天数据</el-button
      >
    </div>
    <div class="gray-text">门店列表</div>

    <!-- 门店筛选按钮区域 -->
    <el-tag
      v-for="store in storeTags"
      :key="store.id"
      :type="store.id === selectedStore ? 'success' : ''"
      @click="handleStoreSelect(store.id)"
      :class="store.id === selectedStore ? 'store-btn active-btn' : 'store-btn'"
    >
      {{ store.name }}
    </el-tag>

    <div class="filter-tip">选择日期后需手动点击查询按钮查询数据</div>

    <!-- 灰色时间标签 -->
    <label for="" class="gray-text small-font">时间</label>

    <!-- 日期筛选区域 -->
    <el-date-picker
      v-model="startDate"
      type="date"
      placeholder="开始日期"
      value-format="YYYY-MM-DD"
      class="small-font"
    />
    <span class="date-separator gray-text">至</span>
    <el-date-picker
      v-model="endDate"
      type="date"
      placeholder="结束日期"
      value-format="YYYY-MM-DD"
      class="small-font"
    />

    <!-- 绿色查询按钮 & 无背景导出按钮 -->
    <el-button type="success" @click="handleQuery" class="query-btn"
      >查询</el-button
    >

    <el-button type="primary" @click="handleExport" plain class="export-btn"
      >导出(Excel)</el-button
    >

    <!-- 数据表格区域（底部灰色边框） -->
    <el-table
      :data="tableData"
      border
      height="70%"
      style="width: 100%; margin-top: 20px"
      class="gray-bottom-border"
    >
      <el-table-column prop="Store" label="店别" />
      <el-table-column prop="Date" label="日期" />
      <el-table-column prop="Week" label="周" />
      <el-table-column prop="TimeSegment" label="时段" />
      <el-table-column prop="ReserveNum" label="预定数" />
      <el-table-column prop="CancelNum" label="取消数" />
      <el-table-column prop="WalkinNum" label="自来客" />
      <el-table-column prop="DirectNum" label="直落数" />
      <el-table-column prop="CheckinNum" label="开房数" />
    </el-table>
    <el-pagination
      size="small"
      background
      layout="prev, pager, next"
      :total="pagination.Records"
      :page-size="20"
      v-model:current-page="pagination.current"
      class="mt-4"
      @current-change="handlePaginationChange"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import {
  ElTag,
  ElDatePicker,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessage
} from "element-plus";

import {
  GetSummaryStoreTimeSlotDailyRecord,
  GetSummaryStoreTimeSlotDailyListRecord
} from "@/api/reservationTimeSlo";
// import { utils } from "./modules/utils";
import * as XLSX from "xlsx"; // 导入xlsx库
// 是否已查询标记
const hasQueried = ref(false);

// 分页数据绑定
const pagination = ref({
  Records: 0,
  Total: 0,
  current: 1
});

// 分页方法
const handlePaginationChange = page => {
  reqBody.value["Paging.Page"] = page;
  // pagination.value.current = page;
  console.log(page);

  GetSummaryStoreTimeSlotDailyRecord(reqAllbody.value).then(res => {
    console.log(res.data);
    const formattedData = res.data.list.map(item => {
      console.log("转换前:", item.Date);
      const dateStr = item.Date.split("T")[0]; // 截取日期部分：2025-06-05
      const formattedDate = dateStr.replace(/-/g, ""); // 替换横线：20250605

      return {
        ...item,
        Store: storeIdToName.value[item.Store] || "未知门店", // 用ID查名称
        Date: formattedDate
      };
    });
    console.log(formattedData);
    tableData.value = formattedData;
  });
};
// 门店标签数据
const storeTags = ref([
  { name: "缤滨店", id: 2 },
  { name: "天河店", id: 3 },
  { name: "英德店", id: 4 },
  { name: "番禺店", id: 5 },
  { name: "海印店", id: 6 },
  { name: "白云店", id: 8 },
  { name: "区庄店", id: 9 },
  { name: "罗湖店", id: 10 },
  { name: "名堂旗舰店", id: 11 }
  // { name: "清远店", id: 10 },
  // { name: "天河名堂店", id: 11 },
  // { name: "区庄名堂店", id: 12 }
]);
const selectedStore = ref(null); // 存储数字ID

const handleStoreSelect = storeId => {
  selectedStore.value = storeId;
  console.log("选中门店ID:", storeId); // 例如点击"缤滨店"会输出 1
};

// 日期选择
const startDate = ref();
const endDate = ref();

// 表格数据
const tableData = ref([]);
const pagesTotal = ref(10); // 初始值
// // 获取标签类型的方法
// const getTagType = store => {
//   return store === selectedStore.value ? "primary" : "";
// };

// 处理门店选择
// const handleStoreSelect = store => {
//   selectedStore.value = store;
// };
const reqBody = ref({
  "Paging.Rows": 20,
  "Paging.Page": pagination.value.current,
  "Paging.Sidx": "CreateTime",
  "Paging.Sord": "desc",
  "Paging.Records": "1"
});
const reqOut = computed(() => ({
  "Paging.Rows": pagesTotal.value, // 使用 .value 获取最新值
  "Paging.Page": 1,
  "Paging.Sidx": "CreateTime",
  "Paging.Sord": "desc",
  "Paging.Records": "1"
}));
watch(pagesTotal, newValue => {
  reqOut.value["Paging.Rows"] = newValue;
});
const reqOutbody = computed(() => ({
  ...reqOut.value,
  StoreID: selectedStore.value,
  StartTime: startDate.value, // 自动响应式更新
  EndTime: endDate.value // 自动响应式更新
}));
const reqAllbody = computed(() => ({
  ...reqBody.value,
  StoreID: selectedStore.value,
  StartTime: startDate.value, // 自动响应式更新
  EndTime: endDate.value // 自动响应式更新
}));
console.log("页面值", {
  StoreID: selectedStore.value,
  StartTime: startDate.value,
  EndTime: endDate.value
});

const storeIdToName = computed(() => {
  const map = {};
  storeTags.value.forEach(store => {
    map[store.id] = store.name;
  });
  return map;
});
// 检查查询条件是否有效（门店和日期都有值）
const isValidQuery = computed(() => {
  return selectedStore.value !== null && startDate.value && endDate.value;
});
// 日期搜索变量
const searchDate = ref("");
// 统计当天数据方法
const handleDailyStats = () => {
  if (!searchDate.value) {
    ElMessage.warning("请输入日期");
    return;
  }

  // 验证日期格式（简单验证）
  if (!/^\d{4}-\d{2}-\d{2}$/.test(searchDate.value)) {
    ElMessage.warning("日期格式不正确，请使用YYYY-MM-DD格式（如：2025-06-01）");
    return;
  }
  //
  GetSummaryStoreTimeSlotDailyListRecord({
    Comedate: searchDate.value.replace(/-/g, "")
  })
    .then(res => {
      // 处理返回数据
      console.log(res);
      ElMessage.success(`${searchDate.value}的当天数据已存入表`);
    })
    .catch(error => {
      console.error("统计当天数据失败:", error);
      ElMessage.error("统计当天数据失败");
    });
};
// 处理查询
const handleQuery = () => {
  if (!isValidQuery.value) {
    ElMessage.warning("请选择门店和日期");
    return;
  }
  pagination.value.current = 1;
  reqBody.value["Paging.Page"] = 1;
  //调用 API 获取数据

  GetSummaryStoreTimeSlotDailyRecord(reqAllbody.value).then(res => {
    console.log(res);
    console.log(res.data.paging.Records);

    pagination.value.Records = res.data.paging.Records;
    pagesTotal.value = res.data.paging.Records;
    console.log(reqAllbody.value);
    const formattedData = res.data.list.map(item => {
      const dateStr = item.Date.split("T")[0]; // 截取日期部分：2025-06-05
      const formattedDate = dateStr.replace(/-/g, ""); // 替换横线：20250605

      return {
        ...item,
        Store: storeIdToName.value[item.Store] || "未知门店", // 用ID查名称
        Date: formattedDate
      };
    });
    console.log(formattedData);
    tableData.value = formattedData;
    hasQueried.value = true;
    ElMessage.success("查询成功");
  });
};
onMounted(() => {
  GetSummaryStoreTimeSlotDailyRecord(reqBody.value).then(res => {
    console.log(res);
    pagination.value.Records = res.data.paging.Records;
    const formattedData = res.data.list.map(item => {
      console.log("转换前:", item.Date);
      const dateStr = item.Date.split("T")[0]; // 截取日期部分：2025-06-05
      const formattedDate = dateStr.replace(/-/g, ""); // 替换横线：20250605

      return {
        ...item,
        Store: storeIdToName.value[item.Store] || "未知门店", // 用ID查名称
        Date: formattedDate
      };
    });
    console.log(formattedData);
    tableData.value = formattedData;
  });
});
// 处理导出
const handleExport = () => {
  if (!hasQueried.value) {
    // 检查是否已查询
    ElMessage.warning("请先执行查询操作");
    return;
  }

  ElMessage.info("正在准备导出数据...");
  console.log(reqOutbody);
  // 调用API获取数据
  GetSummaryStoreTimeSlotDailyRecord(reqOutbody.value)
    .then(res => {
      console.log(res);

      if (!res.data || !res.data.list || res.data.list.length === 0) {
        ElMessage.warning("没有数据可导出");
        return;
      }

      // 格式化数据用于导出
      const formattedData = res.data.list.map(item => {
        const dateStr = item.Date.split("T")[0]; // 截取日期部分：2025-06-05
        const formattedDate = dateStr.replace(/-/g, ""); // 替换横线：20250605
        return {
          店别: storeIdToName.value[item.Store] || "未知门店",
          日期: formattedDate || "",
          周: item.Week || "",
          时段: item.TimeSegment || "",
          预定数: item.ReserveNum || 0,
          取消数: item.CancelNum || 0,
          自来客: item.WalkinNum || 0,
          直落数: item.DirectNum || 0,
          开房数: item.CheckinNum || 0
        };
      });

      // 创建工作簿和工作表
      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "门店数据");

      // 生成文件名
      const storeName = storeIdToName.value[selectedStore.value] || "所有门店";
      const fileName = `${storeName}_数据_${new Date().toISOString().slice(0, 10)}.xlsx`;

      // 导出Excel文件
      XLSX.writeFile(wb, fileName);

      ElMessage.success("导出成功");
    })
    .catch(error => {
      console.error("导出失败:", error);
      ElMessage.error("导出失败，请重试");
    });
};
</script>

<style lang="scss" scoped>
.store-list-container {
  padding: 20px;

  // 门店列表标题灰色
  .gray-text {
    color: #666; // 灰色文字颜色
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 4px; // 与下方内容的间距
  }

  // 时间相关字体调小
  .small-font {
    font-size: 14px; // 调整字体大小
    margin-right: 60px;
    color: #9b9898;
  }

  // 门店按钮样式
  ::v-deep .store-btn {
    margin-right: 10px;
    cursor: pointer;
    background-color: #249672;
    border-color: #249672; // 绿色边框（默认状态）
    color: white;

    &.active-btn {
      background-color: #ff7f50; // 点击后橙色背景
      border-color: #ff7f50; // 点击后橙色边框
      color: white;
    }
  }
  .date-separator {
    font-size: 12px;
    margin: 10px;
  }
  // 按钮统一大小
  .query-btn,
  .export-btn {
    min-width: 80px;
    height: 34px;
    margin-left: 10px;
  }

  // 导出按钮无背景（通过plain修饰符实现）
  .export-btn {
    background-color: transparent;
    color: #47b960; // 保持绿色文字
    border-color: #47b960;
  }
  .filter-tip {
    margin-left: 80px;
    font-size: 10px;
    color: red;
  }
  // 表格底部灰色边框
  // .gray-bottom-border {
  //   ::v-deep .el-table__header-wrapper {
  //     border-bottom: 1px solid #e0e0e0; // 顶部边框保持默认
  //   }
  //   ::v-deep .el-table__body-wrapper {
  //     border-bottom: 1px solid #e0e0e0; // 底部灰色边框
  //   }
  // }
}
</style>
