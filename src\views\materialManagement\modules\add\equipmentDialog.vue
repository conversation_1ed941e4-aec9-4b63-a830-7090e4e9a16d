<script setup>
import "./addprogramList.css"
import { getData } from "./getData"
import { toRefs } from "vue";
// 接收父组件的值
const props = defineProps({
  equipmentDialog: Boolean,
});
const { params, pagination, selectEquiment,equipmentList,getEquimentList} = getData();
// 解构后在模板中可以直接使用 `show` 和 `changeData`
const { equipmentDialog } = toRefs(props);
const handleCurrentChange =async () => {
  params.value.Page = pagination.value.current
  await getEquimentList(); 
}

const handleSelectEquipment = item => {
  if (item.selected) {
    selectEquiment.value.push(item);
  } else {
    selectEquiment.value = selectEquiment.value.filter(
      i => i.DeviceID !== item.DeviceID
    );
  }
};
//关闭弹窗后执行方法
const emit = defineEmits(['update:equipmentDialog', 'refreshParentData']);
const equipmentDialogClose =()=>{
  console.log('关闭事件触发'); // 验证日志
  emit('update:equipmentDialog', false);
}
const sureSelectEquipment = () => {
  console.log(selectEquiment.value);
  emit('update:equipmentDialog', false);
};

</script>
<template>
  <div>
    <el-dialog
        v-model="equipmentDialog"
        width="1000"
        draggable
        title="选择播放设备"
        append-to-body
        aligin-center="true"
        style="max-height: 600px"
        @close="equipmentDialogClose"
      >
        <el-scrollbar height="400px">
          <!-- <div class="material-container"> -->
          <el-row :gutter="4">
            <el-col
              :span="12"
              v-for="item in equipmentList"
              style="margin-bottom: 10px"
            >
              <el-row>
                <el-col :span="2">
                  <el-checkbox
                    v-model="item.selected"
                    :true-value="item.DeviceID"
                    @change="handleSelectEquipment(item)"
                    style="margin-left: 10px"
                  ></el-checkbox>
                </el-col>
                <el-col :span="22">
                  <el-card
                    :key="item.DeviceID"
                    style="
                      max-width: 350px;
                      height: 150px;
                      display: flex;
                      align-items: center;
                      gap: 20px;
                    "
                  >
                  <el-row>
                    <el-col :span="8">
                      <img
                      src="@/assets/equiment.png"
                      style="height: 100px; flex-shrink: 0; margin-right: 30px"
                    />
                    </el-col>
                    <el-col :span="16">
                      <div style="text-align: left; float: right">
                      <p>
                        名称：<span class="equMsg">{{ item.DeviceName }}</span>
                      </p>
                      <p>
                        型号：<span class="equMsg">{{ item.DeviceModel }}</span>
                      </p>
                      <p>
                        尺寸：<span class="equMsg">{{
                          item.DeviceResolution
                        }}</span>
                      </p>
                      <p>
                        形状：<span class="equMsg">{{
                          item.DeviceOrientation
                        }}</span>
                      </p>
                      <p
                        >状态：<span class="equMsg"
                          ><el-tag
                            v-if="item.Status === 0"
                            :key="item.DeviceID"
                            type="info"
                            effect="light"
                            round
                          >
                           离线
                          </el-tag>
                          <el-tag
                            v-if="item.Status=== 1"
                            :key="item.DeviceID"
                            type="success"
                            effect="light"
                            round
                          >
                             在线
                          </el-tag>
                        </span></p
                      >
                    </div>
                    </el-col>
                  </el-row>
                  </el-card>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <!-- </div> -->
        </el-scrollbar>
        <el-pagination
    size="small"
    background
    layout="prev, pager, next"
    :total="pagination.Records"
     v-model:current-page="pagination.current"
    class="mt-4"
     @current-change="handleCurrentChange"
  />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="equipmentDialogClose">取消</el-button>
            <el-button type="primary" @click="sureSelectEquipment">
              确定选择
            </el-button>
          </div>
        </template>
      </el-dialog>
  </div>
</template>
