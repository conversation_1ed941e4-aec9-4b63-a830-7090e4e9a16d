
<script lang="ts" setup>
import { reactive, ref, computed } from 'vue'
import "./utils/index.scss"
import roomList from './roomList.vue'

const isDrawerOpen = ref(false);
import { useUser } from "./utils/hook";
// 打开抽屉时，给页面主体添加右侧 margin
const openDrawer = () => {
  console.log("打开抽屉");
  isDrawerOpen.value = true;
};

// 关闭抽屉时，移除页面主体的右侧 margin
const closeDrawer = () => {
  isDrawerOpen.value = false;
};

// 动态生成楼层数和房间数
function generateBuildingData() {
  const totalFloors = 5;          // 总楼层数
  const totalRooms = 150;         // 总房间数
  const roomsPerFloorMin = 20;    // 每层最少房间数
  const roomsPerFloorMax = 40;    // 每层最多房间数
  
  // 创建楼层数组
  const floors = [];
  let remainingRooms = totalRooms;
  
  // 为前4层随机分配房间数
  for (let i = 0; i < totalFloors - 1; i++) {
    // 确保剩余房间数足够分配给后续楼层
    const maxRoomsThisFloor = Math.min(
      roomsPerFloorMax, 
      remainingRooms - (totalFloors - i - 1) * roomsPerFloorMin
    );
    
    const roomsThisFloor = Math.floor(
      Math.random() * (maxRoomsThisFloor - roomsPerFloorMin + 1)
    ) + roomsPerFloorMin;
    
    floors.push({
      number: i + 1,  // 从2层开始
      rooms: generateRooms(roomsThisFloor, i + 1)
    });
    remainingRooms -= roomsThisFloor;
  }
  
  // 最后一层分配剩余所有房间
  floors.push({
    number: totalFloors,  // 最后一层的编号
    rooms: generateRooms(remainingRooms, totalFloors)
  });
  
  return floors;
}

// 生成指定数量的房间数据
function generateRooms(count, floorNumber) {
  const rooms = [];
  
  for (let i = 1; i <= count; i++) {
    // 房间号格式：楼层号+房间序号
    const roomNumber = `${floorNumber}0${i}`;
    
    // 随机状态：70%概率为available，30%概率为occupied
    const status = Math.random() > 0.7 ? 'occupied' : 'available';
    
    rooms.push({
      number: roomNumber,
      status: status
    });
  }
  
  return rooms;
}

// 生成数据并打印
const floors = generateBuildingData();// const floors = [
//   {
//     number: 2,
//     rooms: [
//       { number: '201', status: 'available' },
//       { number: '202', status: 'available' },
//       { number: '301', status: 'occupied' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       // 其他房间数据...
//     ]
//   },
//   {
//     number: 3,
//     rooms: [
//       { number: '301', status: 'occupied' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       // 其他房间数据...
//     ]
//   },
//   {
//     number: 4,
//     rooms: [
//       { number: '301', status: 'occupied' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       // 其他房间数据...
//     ]
//   },
//   {
//     number: 4,
//     rooms: [
//       { number: '301', status: 'occupied' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       { number: '302', status: 'available' },
//       // 其他房间数据...
//     ]
//   },
//   // 其他楼层数据...
// ]
// 获取状态文本
const getStatusText = (status)=> {
      switch(status) {
        case 'available': return '可用';
        case 'occupied': return '已占用';
        case 'checkout': return '待结账';
        default: return status;
      }
    }
const form = reactive({
  name: '',
  region: '',
  date1: '',
  date2: '',
  delivery: false,
  type: [],
  resource: '',
  desc: '',
  tel: '',
  roomNo: '',
  roomType: '',
  date: '',
  time: ''
});
const tableData = [
{ date: '2023-05-20', name: '张三', phone: '13800138000', number: 2, deposit: 500, address: '已付' },
{ date: '2023-05-21', name: '李四', phone: '13900139000', number: 3, deposit: 800, address: '未付' },
]

const {
 
  openDialog,bookListData
  
} = useUser();
</script>
<template>
  <div style="width: 100%; background-color: white; margin: 0px; height: 100%; transition: margin-right 0.3s ease;">
    <div 
      class="main-content" 
     :style="{ marginRight: isDrawerOpen ? '400px' : '0', transition: 'margin-right 0.3s ease' }"
    >
    <div class="main-header">
      <div class="header-left">
        <div class="havepeople">
        <div class="border-title">
      <span>带客(600)</span>
    </div>
      <span class="grid-contemt ep-bg-orange">结账(300)</span>
      <span class="grid-contemt ep-bg-purple">可用(300)</span>
    </div>
    <el-divider direction="vertical" style="height:30px; margin:0px" />
    <div class="havepeople">
      <div class="border-title">
      <span>空置(600)</span>
    </div>
      <span class="grid-contemt ep-bg-orange">结账(300)</span>
      <span class="grid-contemt ep-bg-purple">可用(300)</span>
    </div>
    <el-divider direction="vertical" style="height:30px; margin:0px" />
    <div class="havespecial">
      <div class="grid-contemt ep-bg-wechat" style="display: inline-block; margin-right: 10px;">
      <IconifyIconOnline
      icon="ic:baseline-wechat"
      width="20px"
      height="20px"
      style="display:flex;margin: 0 auto;"
    />
        微信(67)
    </div>
      <div class="grid-contemt ep-bg-birth" style="display: inline-block;">
      <IconifyIconOnline
      icon="icon-park-solid:birthday-cake"
      width="20px"
      height="20px"
    style="display:flex;margin: 0 auto;"
    />
        生日(78)
      </div>
    </div>
      </div>
      <div class="header-right" v-if="isDrawerOpen===false">
        <el-form-item label="查询" style="margin-bottom: 0px;">
      <el-input />
    </el-form-item>
    <el-button>搜索</el-button>
    <el-button @click="openDrawer()" style="margin-right:10px;">预约开房</el-button>
      </div>
    </div>
  <el-divider style="margin: 0px;"/>
  <roomList />
  <!-- <div class="floor-container">
    <div v-for="(floor, floorIndex) in floors" :key="floorIndex" class="floor-wrapper">
      <span class="floor-title">
        <span class="floor-number">区域{{ floor.number }}</span>
      </span>
      <div class="room-flexbox">
        <div 
          v-for="(room, roomIndex) in floor.rooms" 
          :key="roomIndex" 
          class="room-item"
          :class="{ 'bg-available': room.status === 'available', 'bg-occupied': room.status === 'occupied' }"
        >
          <div class="room-number">{{ room.number }}</div>
          <div class="room-status">{{ getStatusText(room.status) }}</div>
        </div>
      </div> -->

      <!-- <el-row :gutter="6" style="width: 100%; padding: 10px;"> -->
        <!-- 内层循环：遍历当前楼层的每个房间 -->
        <!-- <el-col :span="1.5"
          v-for="(room, roomIndex) in floor.rooms" 
          :key="roomIndex"
        >
          <div 
            class="room-item"
            :class="{
              'bg-available': room.status === 'available',
              'bg-occupied': room.status === 'occupied',
            }"
          >
            <div class="room-number">{{ room.number }}</div>
            <div class="room-status">
              {{getStatusText(room.status)}}
            </div>
          </div>
        </el-col>
      </el-row> -->
    <!-- </div>
    </div> -->
  </div>
  <div 
      class="side-drawer" 
      :class="{ 'is-open': isDrawerOpen }"
    >
      <div class="drawer-header">
        <el-button @click="closeDrawer" type="primary" size="small">关闭</el-button>
      </div>
      <div class="drawer-content">
        <!-- 侧边栏内容，如表单、菜单等 -->
         <el-row :gutter="4">
          <el-col :span="12">
            <el-form-item label="时段" size="small">
            <el-select placeholder="请选择时段" v-model="form.region">
        <el-option label="平常时段" value="shanghai" />
        <el-option label="黄金时段" value="beijing" />
      </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="查询" size="small" v-model="form.name">
            <el-input placeholder="电话/预约号/姓氏"></el-input>
          </el-form-item>
        </el-col>

         </el-row>
         <el-table :data="tableData" border style="max-width: 100%" size="small" table-layout="auto">
    <el-table-column prop="date" label="预约号" width="80"/>
    <el-table-column prop="name" label="姓名" width="50"/>
    <el-table-column prop="phone" label="电话" width="80" />
    <el-table-column prop="number" label="人数" width="39"/>
    <el-table-column prop="deposit" label="订金" width="39"/>
    <el-table-column prop="address" label="已付" />
   </el-table>
  <div>
    <span class="tableTotal">总数:2</span>
    <span class="tableTotal">超时:7</span>
    <span class="tableTotal">折扣:10</span>
    <span class="tableTotal">订金:0</span>
  </div>
  <el-divider style="margin-top: 10px;"/> 
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item label="姓名" size="small" v-model="form.name">
            <el-input placeholder="请输入姓名"></el-input>
          </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-radio-group v-model="form.resource">
        <el-radio value="Sponsor">先生</el-radio>
        <el-radio value="Venue">女士</el-radio>
      </el-radio-group>
    </el-col>
    <el-col :span="12">
      <el-form-item label="电话" size="small" v-model="form.name">
            <el-input placeholder="请输入电话"></el-input>
          </el-form-item>
    </el-col>
  </el-row>
  <div>
    <p class="msg">预定信息</p>
    <p style="margin-top: 10px; font-size: 14px;"><span class="t-g">房型:</span>VIP房 | <span class="msgtime"><span class="t-g">到达:</span>20:00</span> |<span class="msgtime"><span class="t-g">结束时段:</span>20:00</span></p>
    <p style="margin-top: 10px; font-size: 14px; margin-bottom: 10px;">留言:美团用券</p>
    <p class="msg">选择信息</p>
    <p style="margin-top: 10px; font-size: 14px;"><span class="t-g">房号:</span>001 | <span class="msgtime"><span class="t-g">房型:</span>VIP房间</span> | <span class="t-g">当前:  <el-select style="display: inline-block; width: 100px;" placeholder="请选择时段" v-model="form.region">
        <el-option label="平常时段" value="shanghai" />
        <el-option label="黄金时段" value="beijing" />
      </el-select></span></p>
      <el-form-item label="开房备注"  size="small" style="margin-top: 10px; width: 92%;">
      <el-input v-model="form.desc" type="textarea" />
    </el-form-item>
  </div>
  <div class="button-container">
    <el-button>开房</el-button>
    <el-button>派房</el-button>
  </div>
      </div>
    </div>


     <!-- <el-button   @click="openDialog()">新增用户</el-button>
  <div class="flex_box">
    <div class="no_flex">
      <bookList></bookList>
    </div>
    <div class="flex1 flex_box">
      <div class="flex1">
        <div>

          <div>
            <el-form :model="form">
              <el-form-item label="日期">
                <el-col :span="5">
                  <el-date-picker v-model="form.date" type="date" placeholder="日期" />
                </el-col>
                <el-col :span="2">
                  <span>时段</span>
                </el-col>

                <el-col :span="2">
                  <span>房型</span>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="form.region" placeholder="时段">
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-col>
              </el-form-item>

              <el-form-item>
                <el-col :span="6">
                  <el-form-item label="姓名">
                    <el-input v-model="form.name" />
                  </el-form-item>
                </el-col>
                <el-col :span="4" class="text-center">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="先生" />
                    <el-radio label="女士" />
                  </el-radio-group>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电话">
                    <el-input v-model="form.tel" />
                  </el-form-item>
                </el-col>
                <el-col :span="2" class="text-center"><el-switch v-model="form.delivery" /></el-col>
              </el-form-item>
              <el-form-item label="房号">
                <el-col :span="5">
                  <el-input v-model="form.roomNo" />
                </el-col>
                <el-col :span="2">
                  <span>房型</span>
                </el-col>
                <el-col :span="5">
                  <el-input v-model="form.roomType" />
                </el-col>
                <el-col :span="2">
                  <span>房型</span>
                </el-col>
                <el-col :span="5">
                  <el-input v-model="form.roomType" />
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div></div>
      </div>
      <div class="no_flex"></div>
    </div>
  </div>
  <div>
      <roomList></roomList>
  </div> -->
  </div>
</template>