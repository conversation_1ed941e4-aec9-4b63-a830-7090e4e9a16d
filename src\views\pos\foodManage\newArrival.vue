<template>
  <!-- 创建新商品 -->
  <div>
    <el-dialog
      v-model="newArrivalDialog"
      draggable
      @close="newArrivalDialogClose"
      width="550px"
      top="3vh"
      title="商品信息"
    >
      <el-form :model="form" label-width="auto" style="width: 500px">
        <el-form-item label="商品名称:">
          <el-input v-model="form.FdCName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品类别:">
          <el-select v-model="form.FtNo" placeholder="请选择商品类别" clearable>
            <el-option label="饮品" value="1" />
            <el-option label="甜点" value="2" />
            <el-option label="主食" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="市场价:">
          <el-input v-model="form.MarketPrice" placeholder="请输入市场价" />
        </el-form-item>
        <el-form-item label="实际售价:">
          <el-input v-model="form.SalePrice" placeholder="请输入实际售价" />
        </el-form-item>
        <el-form-item label="价格模式：">
          <el-radio-group v-model="form.PriceMode">
            <el-radio value="周日到周四">周日到周四</el-radio>
            <el-radio value="周五到周六">周五到周六</el-radio>
            <el-radio value="周六到周日">周六到周日</el-radio>
            <el-radio value="周一到周五">周一到周五</el-radio>
            <el-radio value="周一到周日">周一到周日</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="适用门店">
          <el-select
            v-model="form.ApplicableStores"
            placeholder="门店选择"
            multiple
            collapse-tags
            :max-collapse-tags="4"
          >
            <el-option
              v-for="store in storeOptions"
              :key="store.value"
              :label="store.label"
              :value="store.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐：">
          <el-switch v-model="form.delivery" />
        </el-form-item>
        <el-table
          v-if="
            form.delivery &&
            addPackageTableData &&
            addPackageTableData.length > 0
          "
          :data="addPackageTableData"
          border
          stripe
          size="small"
          style="margin-bottom: 10px"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            label="套餐内商品编号"
            prop="PGFdNo"
            min-width="80"
          />
          <el-table-column
            label="套餐内商品名称"
            prop="PGFdCName"
            min-width="80"
          />
          <el-table-column label="套餐内商品数量" min-width="90" align="center">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.PGFdQty"
                :min="1"
                size="small"
                controls-position="right"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="套餐内商品价格"
            prop="CommodityPrice"
            min-width="120"
            align="right"
          >
            <template #default="scope">
              {{ scope.row.CommodityPrice }} 元
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                link
                @click="removePackageItem(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          v-if="form.delivery"
          class="mt-1"
          style="width: 100%; height: 25px; font-size: small"
          @click="existArrival"
        >
          {{
            addPackageTableData && addPackageTableData.length > 0
              ? "修改套餐商品"
              : "选择已有商品"
          }}
        </el-button>
        <div class="button-container">
          <el-button type="primary" @click="addNewArrival">保存</el-button>
          <el-button @click="newArrivalDialogClose">取消</el-button>
        </div>
      </el-form>
    </el-dialog>
    <chooseExistArrival
      v-model:existArrivalDialog="existArrivalDialog"
      @select-product="handleProductSelect"
      v-model:forceCommodityMode="forceCommodityMode"
      source="newArrival"
    />
  </div>
</template>
<script setup>
import { ref, toRefs, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { utils } from "./modules/utils";
import chooseExistArrival from "./existArrival.vue";
const {
  addFoodManangeTable,
  form,
  addPackageTableData,
  isAdd,
  isView,
  generateUniqueId,
  filteredData
} = utils();
onMounted(() => {
  addPackageTableData.value = [];
});
const props = defineProps({
  newArrivalDialog: Boolean
});
// 解构
const { newArrivalDialog } = toRefs(props);

// 关闭事件 传递给父组件
const emit = defineEmits(["update:newArrivalDialog", "refreshParentData"]);
const newArrivalDialogClose = () => {
  clearData();
  newArrivalDialog.value = false;
  emit("update:newArrivalDialog", false);
};
const removePackageItem = index => {
  addPackageTableData.value.splice(index, 1);
};
const addNewArrival = () => {
  const validData = addPackageTableData.value.filter(
    item => item.PGFdCName.trim() !== ""
  );
  console.log(form.value);
  console.log("有效数据:", validData);
  const result = ref({});

  if (form.value.delivery == true) {
    console.log("套餐");
    // 组合数据为团购套餐
    result.value = {
      Name: form.value.Name,
      ComboName: form.value.FdCName,
      MarketPrice: form.value.MarketPrice,
      SalePrice: form.value.SalePrice,
      PriceMode: form.value.PriceMode,
      delivery: form.value.delivery,
      ApplicableStores: form.value.ApplicableStores.join(","),
      FtNo: form.value.FtNo,
      Type: "2",
      PackageItems: validData
    };
  } else {
    console.log("单个商品");
    // 仅使用表单数据作为单个商品
    result.value = {
      Name: form.value.Name,
      FdCName: form.value.FdCName,
      MarketPrice: form.value.MarketPrice,
      SalePrice: form.value.SalePrice,
      PriceMode: form.value.PriceMode,
      delivery: form.value.delivery,
      FtNo: form.value.FtNo,
      Type: "1",
      ApplicableStores: form.value.ApplicableStores.join(",")
    };
  }
  // 打印或处理结果
  if (isAdd.value == true) {
    console.log("新增数据:", result.value);
    result.value.id = generateUniqueId(); // 生成唯一ID
    filteredData.value.unshift(result.value);
  } else {
    // 修改模式
    const targetId = form.value.id;
    console.log(targetId);
    console.log("filteredData:", filteredData.value);
    const index = filteredData.value.findIndex(item => item.id === targetId);
    console.log(index);
    if (index !== -1) {
      // 保留原数据的唯一标识和其他必要字段
      const originalItem = filteredData.value[index];
      // 合并新旧数据（保留原ID等关键字段）
      const updatedItem = {
        ...originalItem, // 保留原数据
        ...result.value, // 应用新数据
        id: originalItem.id,
        FdNo: originalItem.FdNo || result.value.FdNo, // 确保ID不变
        ComboNo: originalItem.ComboNo || result.value.ComboNo
      };

      // 使用Vue的响应式更新
      filteredData.value.splice(index, 1, updatedItem);

      const viewIndex = isView.value.findIndex(item => item.id === targetId);
      if (viewIndex !== -1) {
        isView.value.splice(viewIndex, 1, { ...updatedItem });
      }

      ElMessage({
        type: "success",
        message: "修改成功"
      });
    } else {
      console.warn("未找到匹配项，数据可能已变更");
      // 安全处理：可以提示用户或转为新增
      filteredData.value.unshift(...result);
      // isView?.value?.unshift({ ...result.value });
      ElMessage.warning("原数据不存在，已转为新增");
    }
  }
  newArrivalDialogClose(); // 关闭弹窗
  emit("refreshParentData"); // 通知父组件刷新数据
  console.log(filteredData.value);
};

// const onAddItem = () => {
//   addPackageTableData.value.push({
//     PGFdCName: "",
//     CommodityPrice: "",
//     PGFdQty: 1,
//     PGFdNo: null
//   });
// };

const existArrivalDialog = ref(false);
const search = ref({
  IsCommodity: false
});
const forceCommodityMode = ref(true);
//选择已有商品
const existArrival = () => {
  existArrivalDialog.value = true;
  forceCommodityMode.value = true;
};
const handleProductSelect = selectedProducts => {
  console.log(selectedProducts);
  // 1. 过滤掉表格中已存在的商品（对比 FdNo）
  const existingFdNos = addPackageTableData.value.map(item => item.PGFdNo); // 获取表格中已有的 FdNo
  // console.log(existingFdNos);
  const duplicateProducts = []; // 存储重复的商品（用于提示）

  const newProducts = selectedProducts.filter(product => {
    const isDuplicate = existingFdNos.includes(product.FdNo);
    if (isDuplicate) {
      duplicateProducts.push(product); // 记录重复商品
    }
    return !isDuplicate; // 只保留不重复的商品
  });

  // 2. 如果有重复商品，提示用户
  if (duplicateProducts.length > 0) {
    const duplicateNames = duplicateProducts.map(p => p.FdCName).join("、");

    ElMessage.warning(`以下商品已存在，跳过添加: ${duplicateNames}`);
  }

  // 格式化数据以匹配表格结构
  const formattedItems = newProducts.map(item => ({
    PGFdNo: item.FdNo,
    PGFdCName: item.FdCName,
    CommodityPrice: item.SalePrice,
    PGFdQty: item.FdQty || 1
  }));

  // 合并到现有数据
  addPackageTableData.value = [...addPackageTableData.value, ...formattedItems];
};
// 清空数据方法
const clearData = () => {
  form.value = {
    Name: "",
    FdCName: "",
    MarketPrice: "",
    SalePrice: "",
    PriceMode: "",
    delivery: false,
    ApplicableStores: null,
    FtNo: ""
  };
  addPackageTableData.value = [];
};

// 选择门店的数据
const storeOptions = ref([
  // 初始数据（可选）
  { label: "天河店", value: "1" },
  { label: "缤缤店", value: "2" },
  { label: "海印店", value: "3" },
  { label: "白云店", value: "4" },
  { label: "区庄店", value: "5" },
  { label: "番禺店", value: "6" },
  { label: "罗湖店", value: "7" },
  { label: "英德店", value: "8" },
  { label: "名堂", value: "9" }
]);
</script>

<style>
.button-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  gap: 8px; /* 按钮间距，可选 */
  padding: 16px; /* 内边距，可选 */
}
</style>
