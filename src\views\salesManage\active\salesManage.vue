<script setup>
import { Search, Plus } from "@element-plus/icons-vue";
import { ref, onMounted, watch } from "vue";
import changeSales from "./changeSales.vue";
import {
  GetSalesAndCardsInfoRecord,
  DeleteSalesAndCardsInfoRecord
} from "@/api/activeCoupon/activeCoupon";
import { ElMessage, ElMessageBox } from "element-plus";
import imagePath from "@/assets/image.png"; // 使用别名@指向src目录
import { getData } from "./modules/utils";
const { currentId, onDraggableClick, TypeName } = getData();
// 表格
const Tableloading = ref(false);
const columns = ref([
  {
    label: "活动名称",
    prop: "Name",
    fixed: true
  },
  {
    label: "活动时间",
    prop: "Time",
    slot: "Time"
  },
  {
    label: "删除状态",
    prop: "isActive",
    slot: "isActive",
    width: "100px"
  }
]);

const salesList = ref();

// 编辑弹窗绑定值
const editVisible = ref(false);
const state = ref("");

const edit = val => {
  console.log("val", val);
  currentId.value = val.id;
  editVisible.value = true;
  state.value = "edit";
};
const add = () => {
  editVisible.value = true;
  state.value = "add";
};
const deleteSales = async row => {
  ElMessageBox.confirm("您确定要删除这条记录", "删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const res = await DeleteSalesAndCardsInfoRecord({
        SalesId: row.id,
        CardSheetId: ""
      });
      if (res.state === 200) {
        ElMessage.success("删除成功");
        getSalesData();
      } else {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};

const see = val => {
  currentId.value = val.id;
  editVisible.value = true;
  state.value = "see";
};
const searchKeyword = ref("");
const reqBody = ref({
  TypeName: "Sales",
  "Paging.Rows": "10",
  "Paging.Page": "1",
  "Paging.Sord": "1",
  "Paging.Sidx": "1",
  "Paging.Records": "1",
  ActivityName: searchKeyword.value
});

const getSalesData = async () => {
  Tableloading.value = true;
  reqBody.value["Paging.Page"] = page.value.current;
  reqBody.value["Paging.Rows"] = page.value.size;
  const res = await GetSalesAndCardsInfoRecord(reqBody.value);
  console.log("res", res);
  salesList.value = res.data.list.map(item => {
    return {
      Name: item.title,
      Time: item.begintime + " 至 " + item.endtime,
      isActive: item.IsDelete,
      id: item.SalesId
    };
  });
  page.value.total = res.data.pagination.total;
  Tableloading.value = false;
  // console.log("salesList.value", salesList.value);
};
const page = ref({
  current: 1,
  size: 20,
  total: 0
});
const handleSizeChange = val => {
  page.value.size = val;
  reqBody.value["Paging.Rows"] = val;
  getSalesData();
};
const handleCurrentChange = val => {
  console.log("val", val);
  page.value.current = val;
  reqBody.value["Paging.Page"] = val;
  getSalesData();
};

// 搜索方法
const searchData = async () => {
  reqBody.value.ActivityName = searchKeyword.value;
  reqBody.value["Paging.Page"] = 1;
  page.value.current = 1;
  await getSalesData();
};
onMounted(async () => {
  await getSalesData();
});

// 监听对话框关闭加载数据
watch(
  () => editVisible.value,
  async newVal => {
    if (newVal === false) {
      page.value.current = 1;
      await getSalesData();
    }
  }
);

// 调用查看二维码
const checkCode = val => {
  currentId.value = val.id;
  TypeName.value = "Sales";
  onDraggableClick();
};
</script>

<template>
  <div>
    <el-input
      v-model="searchKeyword"
      placeholder="搜索名称..."
      clearable
      class="search-input"
    >
    </el-input>
    <el-button type="primary" @click="searchData">
      <el-icon><Search /></el-icon>
      查询
    </el-button>

    <el-button type="primary" @click="add">
      <el-icon><Plus /></el-icon>
      新增
    </el-button>
    <!-- <p style="margin-left: 20px; font-size: smaller; color: grey">
      *删除状态中蓝色为未删除灰色为已删除
    </p> -->
    <el-table
      :columns="columns"
      :data="salesList"
      height="82%"
      style="width: 95%; margin: 0px auto"
      v-loading="Tableloading"
    >
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :prop="item.prop"
        :width="item.width"
        :label="item.label"
      >
        <template #default="scope" v-if="item.prop === 'isActive'">
          <el-switch
            v-model="scope.row.isActive"
            :active-value="false"
            :inactive-value="true"
            :disabled="true"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200px">
        <template #default="scope">
          <el-button
            v-if="scope.row.isActive === false"
            link
            type="primary"
            size="small"
            @click="edit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            v-if="scope.row.isActive === false"
            @click="deleteSales(scope.row)"
            >删除</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            v-if="scope.row.isActive === false"
            @click="checkCode(scope.row)"
            >查看二维码</el-button
          >
          <el-button
            link
            type="primary"
            v-if="scope.row.isActive === true"
            size="small"
            @click="see(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page.current"
      v-model:page-size="page.size"
      :page-sizes="[20, 50, 100, 200]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin: 10px 20px 0px 20px"
    />
    <!-- 编辑弹窗 -->
    <changeSales v-model:editVisible="editVisible" v-model:state="state" />
  </div>
</template>

<style scoped>
.search-input {
  width: 300px;
  margin: 10px 10px;
}
</style>
