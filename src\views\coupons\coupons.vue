<template>
  <div>
    <el-input
      v-model="searchKeyword"
      placeholder="搜索名称..."
      clearable
      class="search-input"
    >
    </el-input>
    <el-button type="primary">
      <el-icon><Search /></el-icon>
      查询
    </el-button>

    <pure-table
      :columns="columns"
      :data="salesList"
      style="height: 70%; width: 95%; margin: 0px auto"
    >
      <template #operation="{ row }">
        <el-button link type="primary" size="small" @click="edit(row)">
          编辑
        </el-button>
        <el-button link type="primary" size="small">删除</el-button>
      </template>
    </pure-table>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import { ref } from "vue";
// 表格
const columns = ref([
  {
    label: "活动名称",
    prop: "Name",
    fixed: true
  },
  {
    label: "活动开始时间",
    prop: "StartTime",
    slot: "StartTime"
  },
  {
    label: "活动结束时间",
    prop: "EndTime",

    slot: "EndTime"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]);

const salesList = ref([
  {
    Name: "K+会员促销",

    StartTime: "2023-11-01 00:00:00",
    EndTime: "2023-11-11 23:59:59"
  },
  {
    Name: "K+自助餐4次卡促销",

    StartTime: "2023-11-01 00:00:00",
    EndTime: "2023-11-11 23:59:59"
  },
  {
    Name: "六一八促销",
    StartTime: "2023-11-01 00:00:00",
    EndTime: "2023-11-11 23:59:59"
  }
]);

// 编辑弹窗绑定值
const editVisible = ref(false);
const edit = () => {
  editVisible.value = true;
};
</script>

<style scoped>
.search-input {
  width: 300px;
  margin: 10px 10px;
}
</style>
