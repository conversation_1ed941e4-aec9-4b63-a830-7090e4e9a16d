import { http} from "@/utils/http";
// import { baseUrlOtherApi } from "./utils";
/** 该接口采用 http://127.0.0.1:3290 后端地址 */
//预约时段统计
export const GetSummaryStoreTimeSlotDailyRecord = (params?: object) => {
  return http.request<any>("get", "/SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyRecord", { params },
  );
};
//统计预约时段数据
export const GetSummaryStoreTimeSlotDailyListRecord = (params?: object) => {
  return http.request<any>("get", "/SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyListRecord", { params },
  );
};


