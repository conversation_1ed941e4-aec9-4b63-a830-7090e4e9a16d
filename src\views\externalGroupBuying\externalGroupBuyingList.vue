<template>
  <div>
    <div class="class-management">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索天王食品编号..."
          clearable
          class="search-input"
          @clear="fetchDevices"
          @change="fetchDevices"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="fetchDevices">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-upload
          action="/api/upload"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success"
            ><el-icon><Plus /></el-icon>批量导入Excel</el-button
          >
        </el-upload>
        <el-button type="success" :icon="Download" @click="downloadTemplate">
          下载模板
        </el-button>
        <el-button type="success" @click="showDialog('add')">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </div>
      <el-table :data="modelList" height="85%" style="width: 100%">
        <el-table-column prop="FdNo" label="天王食品编号" width="180" />
        <el-table-column prop="ThirdFdNo" label="外部食品编号" width="180" />
        <el-table-column prop="Qty" label="数量" width="180" />
        <el-table-column prop="StoreName" label="门店别名" width="180" />
        <el-table-column prop="PlatformName" label="平台" width="180" />
        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="showDialog('edit', scope.row)"
            >
              修改
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="delBtnFood(scope.row.Id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.Rows"
        :total="pagination.Records"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handlePaginationChange"
        @current-change="handlePaginationChange"
      />
    </div>
    <!-- 编辑表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增信息' : '编辑信息'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="天王食品编号" label-width="120px" prop="FdNo">
          <el-input v-model="formData.FdNo" />
        </el-form-item>
        <el-form-item label="外部食品编号" label-width="120px" prop="ThirdFdNo">
          <el-input v-model="formData.ThirdFdNo" />
        </el-form-item>
        <el-form-item label="数量" label-width="120px" prop="Qty">
          <el-input v-model.number="formData.Qty" />
        </el-form-item>
        <el-form-item label="门店" label-width="120px" prop="StoreId">
          <el-select
            v-model="formData.StoreId"
            clearable
            placeholder="请选择门店"
            size="large"
          >
            <el-option
              v-for="item in Storelist"
              :key="item.StoreId"
              :label="item.StoreName"
              :value="item.StoreId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="平台" label-width="120px" prop="Platform">
          <el-select
            v-model="formData.Platform"
            placeholder="请选择平台"
            size="large"
          >
            <el-option
              v-for="item in PlatformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="设备方向" prop="DeviceOrientation">
          <el-input v-model="formData.DeviceOrientation" />
        </el-form-item>
        <el-form-item label="设备高度" prop="DeviceHeight">
          <el-input v-model="formData.DeviceHeight" />
        </el-form-item>
        <el-form-item label="设备宽度" prop="DeviceWidth">
          <el-input v-model="formData.DeviceWidth" />
        </el-form-item>
        <el-form-item label="设备分辨率" prop="DeviceResolution">
          <el-input v-model="formData.DeviceResolution" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="在线"
            inactive-text="离线"
          />
        </el-form-item>-->
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </template>
    </el-dialog>
    <!-- 投放任务对话框 -->
    <el-dialog v-model="dialogAdCampaignVisible" width="700px">
      <el-descriptions title="设备信息" :column="2" border>
        <el-descriptions-item label="编码">{{
          AdCampaignShowData.DeviceID
        }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{
          AdCampaignShowData.DeviceName
        }}</el-descriptions-item>
      </el-descriptions>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="投放任务列表" name="AdCampaignlist">
          <el-button type="primary" @click="showCampaignModelDatils"
            >新增投放</el-button
          >
          <el-table
            :data="AdCampaignShowData.mMAdCampaigns"
            style="width: 100%; height: 200px"
          >
            <el-table-column
              prop="CampaignID"
              label="投放任务编码"
              width="180"
            />
            <el-table-column prop="CampaignName" label="任务名称" width="180" />
            <el-table-column label="开始时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.StartTime) }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.EndTime) }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="removeCampaignbtn(scope.$index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="dialogAdCampaignVisible = false">取消</el-button>
        <el-button type="primary" @click="SubmitBdAdCampaign">确认</el-button>
      </template>
    </el-dialog>
    <!-- 选择投放任务的信息框 -->
    <el-dialog
      v-model="CampaigndialogVisible"
      title="投放任务信息"
      width="500px"
    >
      <div class="mt-4">
        <el-button type="primary" @click="revertValueBtn">返回</el-button>
        <el-input
          v-model="searchCampaignKeyword"
          style="width: 200px"
          placeholder="请输入投放任务名称"
          class="input-with-select ml-2"
        >
          <template #append>
            <el-button @click="QueryCampaignBtn" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="投放任务列表" name="AdCampaignlist">
          <el-table
            :data="AdCampaignShowData2"
            row-key="CampaignID"
            style="width: 100%; height: 500px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60" />
            <el-table-column
              prop="CampaignID"
              label="投放任务编码"
              width="180"
            />
            <el-table-column
              prop="CampaignName"
              label="投放任务名称"
              width="180"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- 投放任务列表分页 -->
      <el-pagination
        v-model:current-page="AdCampaignpagination.current"
        v-model:page-size="AdCampaignpagination.Rows"
        size="small"
        background
        layout="prev, pager, next"
        :total="AdCampaignpagination.Records"
        @size-change="handleCampaignPaginationChange"
        @current-change="handleCampaignPaginationChange"
        class="mt-4 ml-14"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import * as XLSX from "xlsx";
import { ref, reactive, onMounted } from "vue";
import {
  Search,
  Plus,
  Edit,
  Delete,
  Key,
  Clock,
  Cpu
} from "@element-plus/icons-vue";
import axios from "axios";
import { saveAs } from "file-saver";
import { ElMessage } from "element-plus";
import {
  getAsyncWayFoodMap,
  AddAsyncWayFoodMap,
  ImportWayFoodMap,
  UpdateAsyncWayFood,
  DeleteAsyncWayFood,
  getAsyncWayStoreMap
} from "@/api/externalGroupBuying/WayFoodMap";
import { getAsyncAdCampaign } from "@/api/materialManage/program.ts";

// 设备数据相关
const loading = ref(false);
const modelList = ref([]);
const Storelist = ref([]);
const searchKeyword = ref("");
//分页
var pagination = reactive({
  current: 1,
  Rows: 20,
  Total: 0,
  Records: 0
});
//投放任务分页
var AdCampaignpagination = reactive({
  current: 1,
  Rows: 5,
  Total: 0,
  Records: 0
});

// 对话框相关
const dialogVisible = ref(false);
const CampaigndialogVisible = ref(false);
const searchCampaignKeyword = ref("");
const selectedRows = ref([]); // 存储选中行数据
// 对话框相关
const dialogAdCampaignVisible = ref(false);
const dialogType = ref("add");
const formData = reactive({
  Id: 0,
  FdNo: "",
  ThirdFdNo: "",
  Platform: "",
  StoreId: "",
  Qty: 0
});
const AdCampaignShowData = ref({});
const Importfile = ref(null);
const AdCampaignShowData2 = ref({});
const PlatformOptions = [
  {
    value: 1,
    label: "美团"
  },
  {
    value: 2,
    label: "抖音"
  },
  {
    value: 3,
    label: "内部"
  }
]; //平台
const activeName = ref("AdCampaignlist");

// 表单验证规则
const formRules = {
  FdNo: [{ required: true, message: "请输入天王食品编号", trigger: "blur" }],
  ThirdFdNo: [
    { required: true, message: "请输入外部食品编号", trigger: "blur" }
  ],
  Qty: [{ required: true, message: "请输入数量", trigger: "blur" }],
  Platform: [{ required: true, message: "请选择平台", trigger: "blur" }]
};

const downloadTemplate = () => {
  // 定义模板数据
  const templateData = [
    {
      天王食品编号: "Dcs1",
      外部食品编号: "23423423",
      数量: 1,
      门店别名: 2,
      平台: "美团",
      "注意：（1）请不要修改第一行的标题列名；（2）导入时，请将2,3,4行已有的测试数据删除；（3）平台只能填写美团、抖音、内部这三种类型":
        ""
    },
    {
      天王食品编号: "Dcs2",
      外部食品编号: "4121325",
      数量: 2,
      门店别名: "",
      平台: "抖音",
      "注意：（1）请不要修改第一行的标题列名；（2）导入时，请将2,3,4行已有的测试数据删除；（3）平台只能填写美团、抖音、内部这三种类型":
        ""
    },
    {
      天王食品编号: "Dcs3",
      外部食品编号: "rw2323",
      数量: 3,
      门店别名: "",
      平台: "内部",
      "注意：（1）请不要修改第一行的标题列名；（2）导入时，请将2,3,4行已有的测试数据删除；（3）平台只能填写美团、抖音、内部这三种类型":
        ""
    }
  ];

  // 创建工作表
  const worksheet = XLSX.utils.json_to_sheet(templateData);

  // 设置列宽
  worksheet["!cols"] = [
    { wch: 15 },
    { wch: 15 },
    { wch: 8 },
    { wch: 8 },
    { wch: 10 },
    { wch: 30 }
  ];

  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "数据模板");

  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, {
    bookType: "xlsx",
    type: "array"
  });

  // 创建Blob并下载
  const data = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  });
  saveAs(data, "团购导入模板.xlsx");
};
//保存投放设备
const SubmitBdAdCampaign = async () => {
  const UpdateLaunchAdCampaign = {
    DeviceID: AdCampaignShowData.value.DeviceID,
    DeviceName: AdCampaignShowData.value.DeviceName,
    mMPlaybackDevice: AdCampaignShowData.value.mMAdCampaigns.map(item => ({
      CampaignID: item.CampaignID,
      DeviceID: AdCampaignShowData.value.DeviceID
    }))
  };
  var response = await UpdateLaunchCampaignAdCampaign(UpdateLaunchAdCampaign);
  ElMessage.success(response.message);
  dialogAdCampaignVisible.value = false;
};
// 显示设备绑定投放任务详情
const showModelDatils = async id => {
  const deleteDevice = {
    DeviceID: id
  };
  var response = await GetByIdAsyncDevice(deleteDevice);
  if (response.data != null) {
    AdCampaignShowData.value = response.data;
    dialogAdCampaignVisible.value = true;
  }
};

// 查询团购门店详情
const getStore = async () => {
  var response = await getAsyncWayStoreMap();
  Storelist.value = response.data;
};

const handleFileChange = async uploadFile => {
  Importfile.value = uploadFile.raw;
  if (!Importfile.value) {
    ElMessage.warning("请先选择文件");
    return;
  }
  try {
    // progressVisible.value = true;
    // progressPercent.value = 0;

    const formData = new FormData();
    formData.append("files", Importfile.value);
    const response = await ImportWayFoodMap(formData);
    if (response.state == "error") {
      ElMessage.error(`导入失败：` + response.message);
    } else {
      ElMessage.success("导入成功");
    }

    //previewData.value = [];
    Importfile.value = null;
    await fetchDevices();
  } catch (error) {
    ElMessage.error("导入失败");
  } finally {
    // setTimeout(() => {
    //   progressVisible.value = false;
    // }, 1000);
  }
};

// 设备分页处理
const handleCampaignPaginationChange = () => {
  showCampaignModelDatils();
};
// 复选框变化时更新选中数据
const handleSelectionChange = val => {
  selectedRows.value = val;
};
//移除投放任务
const removeCampaignbtn = index => {
  AdCampaignShowData.value.mMAdCampaigns.splice(index, 1);
};
//点击返回按钮返回设备数据
const revertValueBtn = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.warning("未选择信息返回！");
    return;
  }
  // 深拷贝选中的数据
  const copiedData = JSON.parse(JSON.stringify(selectedRows.value));

  // 合并到表格中（避免重复）
  const existingIds = AdCampaignShowData.value.mMAdCampaigns.map(
    item => item.CampaignID
  );
  const newData = copiedData.filter(
    item => !existingIds.includes(item.CampaignID)
  );

  AdCampaignShowData.value.mMAdCampaigns = [
    ...AdCampaignShowData.value.mMAdCampaigns,
    ...newData
  ];
  CampaigndialogVisible.value = false;
  selectedRows.value = [];
};
//点击按钮查询设备
const QueryCampaignBtn = () => {
  showCampaignModelDatils();
};
// 显示投放详情
const showCampaignModelDatils = async () => {
  //编写参数对象
  const params = {
    Status: "1",
    AllDates: new Date(Date.now()),
    QueryCriteria: searchCampaignKeyword.value,
    Rows: AdCampaignpagination.Rows,
    Page: AdCampaignpagination.current,
    Sidx: "Id",
    Sord: "desc"
  };
  var response = await getAsyncAdCampaign(params);
  AdCampaignShowData2.value = response.data.list;
  AdCampaignpagination = response.data.paging;
  CampaigndialogVisible.value = true;
};

// 获取设备列表
const fetchDevices = async () => {
  loading.value = true;
  try {
    //编写参数对象
    const params = {
      Status: "1",
      AllDates: new Date(Date.now()),
      QueryCriteria: searchKeyword.value,
      Rows: pagination.Rows,
      Page: pagination.current,
      Sidx: "Id",
      Sord: "desc"
    };
    const response = await getAsyncWayFoodMap(params);
    modelList.value = response.data.list;
    pagination = response.data.paging;
    await getStore();
  } finally {
    loading.value = false;
  }
};

// 显示对话框
const showDialog = (type, value) => {
  dialogType.value = type;
  if (type === "edit") {
    Object.assign(formData, value);
  } else {
    formData.Id = 0;
    formData.FdNo = "";
    formData.ThirdFdNo = "";
    formData.Platform = "";
    formData.StoreId = "";
    formData.Qty = 0;
  }
  dialogVisible.value = true;
};
// 提交表单
const submitForm = async () => {
  // 提交操作
  if (dialogType.value === "add") {
    const newModel = {
      Model: {
        Id: 0,
        FdNo: formData.FdNo,
        ThirdFdNo: formData.ThirdFdNo,
        Platform: formData.Platform,
        StoreId: formData.StoreId == "" ? -1 : formData.StoreId,
        Qty: formData.Qty
      }
    };
    var response = await AddAsyncWayFoodMap(newModel);
    if (response.state == 200) {
      dialogVisible.value = false;
      ElMessage.success(response.message);
      await fetchDevices();
    } else {
      ElMessage.error(response.message);
    }
  } else {
    //修改操作
    const updateModel = {
      Model: {
        Id: formData.Id,
        FdNo: formData.FdNo,
        ThirdFdNo: formData.ThirdFdNo,
        Platform: formData.Platform,
        StoreId: formData.StoreId == "" ? -1 : formData.StoreId,
        Qty: formData.Qty
      }
    };
    var response = await UpdateAsyncWayFood(updateModel);
    if (response.state == 200) {
      dialogVisible.value = false;
      ElMessage.success(response.message);
      await fetchDevices();
    } else {
      ElMessage.error(response.message);
    }
  }
};

// 删除团购信息
const delBtnFood = async id => {
  const deleteModel = {
    Id: id
  };
  var response = await DeleteAsyncWayFood(deleteModel);
  await fetchDevices();
  ElMessage.success(response.message);
};

// 分页处理
const handlePaginationChange = () => {
  fetchDevices();
};

// 日期格式化
const formatDate = date => {
  return new Date(date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 初始化加载
onMounted(fetchDevices);
</script>

<style scoped lang="scss">
.class-management {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;

  .operation-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }
  }

  .class-list {
    margin-bottom: 20px;
    min-height: 500px;
  }

  .class-card {
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .class-header {
      display: flex;
      align-items: center;
      gap: 10px;

      .title-name {
        font-weight: bold;
        font-size: 16px;
      }
    }

    .card-content {
      padding: 10px 0;

      .info-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
          color: #666;
        }

        .label {
          color: #666;
          margin-right: 5px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;

    .search-input {
      width: 100% !important;
    }
  }
}
</style>
